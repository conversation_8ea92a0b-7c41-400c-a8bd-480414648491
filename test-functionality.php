<?php
/**
 * Test script for Gotham Block Extra Light plugin functionality
 * This file can be used to test the plugin's core functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

/**
 * Test database table creation and structure
 */
function test_gotham_database_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_adblock_stats';
    
    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if (!$table_exists) {
        return ['status' => 'error', 'message' => 'Database table does not exist'];
    }
    
    // Check table structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $required_columns = ['id', 'event_type', 'ip_address', 'user_agent', 'browser', 'os', 'country', 'status', 'created_at', 'updated_at'];
    $existing_columns = array_column($columns, 'Field');
    
    $missing_columns = array_diff($required_columns, $existing_columns);
    
    if (!empty($missing_columns)) {
        return ['status' => 'error', 'message' => 'Missing columns: ' . implode(', ', $missing_columns)];
    }
    
    return ['status' => 'success', 'message' => 'Database table structure is correct'];
}

/**
 * Test browser detection function
 */
function test_gotham_browser_detection() {
    $test_cases = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' => 'Chrome',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0' => 'Firefox',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15' => 'Safari',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59' => 'Microsoft Edge',
        '' => 'Unknown'
    ];
    
    $results = [];
    foreach ($test_cases as $user_agent => $expected) {
        $detected = gotham_get_browser($user_agent);
        $results[] = [
            'user_agent' => substr($user_agent, 0, 50) . '...',
            'expected' => $expected,
            'detected' => $detected,
            'status' => $detected === $expected ? 'pass' : 'fail'
        ];
    }
    
    return $results;
}

/**
 * Test OS detection function
 */
function test_gotham_os_detection() {
    $test_cases = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' => 'Windows 10',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15' => 'Mac OS X',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36' => 'Linux',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15' => 'iPhone',
        'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36' => 'Android',
        '' => 'Unknown OS'
    ];
    
    $results = [];
    foreach ($test_cases as $user_agent => $expected) {
        $detected = gotham_get_os($user_agent);
        $results[] = [
            'user_agent' => substr($user_agent, 0, 50) . '...',
            'expected' => $expected,
            'detected' => $detected,
            'status' => $detected === $expected ? 'pass' : 'fail'
        ];
    }
    
    return $results;
}

/**
 * Test tracking function
 */
function test_gotham_tracking() {
    // Simulate tracking a popup display
    $result = gotham_adblock_track_event('pop_displayed', 'pending');
    
    if ($result === false) {
        return ['status' => 'error', 'message' => 'Failed to track event'];
    }
    
    return ['status' => 'success', 'message' => 'Event tracking successful', 'id' => $result];
}

/**
 * Run all tests
 */
function run_gotham_tests() {
    $tests = [
        'Database Table' => test_gotham_database_table(),
        'Browser Detection' => test_gotham_browser_detection(),
        'OS Detection' => test_gotham_os_detection(),
        'Event Tracking' => test_gotham_tracking()
    ];
    
    return $tests;
}

// Only run tests if this is an admin request and user has proper capabilities
if (is_admin() && current_user_can('manage_options') && isset($_GET['gotham_test'])) {
    $test_results = run_gotham_tests();
    
    echo '<div class="wrap">';
    echo '<h1>Gotham Block Plugin Test Results</h1>';
    
    foreach ($test_results as $test_name => $result) {
        echo '<h2>' . esc_html($test_name) . '</h2>';
        
        if (is_array($result) && isset($result['status'])) {
            $status_class = $result['status'] === 'success' ? 'notice-success' : 'notice-error';
            echo '<div class="notice ' . $status_class . '"><p>' . esc_html($result['message']) . '</p></div>';
        } elseif (is_array($result)) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>User Agent</th><th>Expected</th><th>Detected</th><th>Status</th></tr></thead>';
            echo '<tbody>';
            foreach ($result as $test_case) {
                $status_class = $test_case['status'] === 'pass' ? 'success' : 'error';
                echo '<tr>';
                echo '<td>' . esc_html($test_case['user_agent']) . '</td>';
                echo '<td>' . esc_html($test_case['expected']) . '</td>';
                echo '<td>' . esc_html($test_case['detected']) . '</td>';
                echo '<td><span style="color: ' . ($test_case['status'] === 'pass' ? 'green' : 'red') . '">' . esc_html($test_case['status']) . '</span></td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
        }
    }
    
    echo '</div>';
}
