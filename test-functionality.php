<?php
/**
 * Test script for Gotham Block Extra Light plugin functionality
 * This file can be used to test the plugin's core functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

/**
 * Test database table creation and structure
 */
function test_gotham_database_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_adblock_stats';

    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

    if (!$table_exists) {
        return ['status' => 'error', 'message' => 'Database table does not exist'];
    }

    // Check table structure with enhanced columns
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $required_columns = [
        'id', 'event_type', 'ip_address', 'user_agent', 'browser', 'browser_version',
        'os', 'country', 'status', 'user_type', 'session_id', 'referrer_url',
        'page_url', 'session_duration', 'is_mobile', 'screen_resolution',
        'timezone', 'created_at', 'updated_at'
    ];
    $existing_columns = array_column($columns, 'Field');

    $missing_columns = array_diff($required_columns, $existing_columns);

    if (!empty($missing_columns)) {
        return ['status' => 'error', 'message' => 'Missing columns: ' . implode(', ', $missing_columns)];
    }

    // Check indexes
    $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
    $required_indexes = ['PRIMARY', 'ip_address', 'status', 'event_type', 'user_type', 'session_id'];
    $existing_indexes = array_unique(array_column($indexes, 'Key_name'));

    $missing_indexes = array_diff($required_indexes, $existing_indexes);

    if (!empty($missing_indexes)) {
        return ['status' => 'warning', 'message' => 'Database table exists but missing indexes: ' . implode(', ', $missing_indexes)];
    }

    return ['status' => 'success', 'message' => 'Enhanced database table structure is correct'];
}

/**
 * Test browser detection function
 */
function test_gotham_browser_detection() {
    $test_cases = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' => 'Chrome',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0' => 'Firefox',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15' => 'Safari',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59' => 'Microsoft Edge',
        '' => 'Unknown'
    ];
    
    $results = [];
    foreach ($test_cases as $user_agent => $expected) {
        $detected = gotham_get_browser($user_agent);
        $results[] = [
            'user_agent' => substr($user_agent, 0, 50) . '...',
            'expected' => $expected,
            'detected' => $detected,
            'status' => $detected === $expected ? 'pass' : 'fail'
        ];
    }
    
    return $results;
}

/**
 * Test OS detection function
 */
function test_gotham_os_detection() {
    $test_cases = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' => 'Windows 10',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15' => 'Mac OS X',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36' => 'Linux',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15' => 'iPhone',
        'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36' => 'Android',
        '' => 'Unknown OS'
    ];
    
    $results = [];
    foreach ($test_cases as $user_agent => $expected) {
        $detected = gotham_get_os($user_agent);
        $results[] = [
            'user_agent' => substr($user_agent, 0, 50) . '...',
            'expected' => $expected,
            'detected' => $detected,
            'status' => $detected === $expected ? 'pass' : 'fail'
        ];
    }
    
    return $results;
}

/**
 * Test enhanced tracking function
 */
function test_gotham_tracking() {
    // Simulate tracking a popup display
    $result = gotham_adblock_track_event('pop_displayed', 'pending');

    if ($result === false) {
        return ['status' => 'error', 'message' => 'Failed to track event'];
    }

    // Test user classification
    $user_type = gotham_classify_user('127.0.0.1');
    if (empty($user_type)) {
        return ['status' => 'error', 'message' => 'User classification failed'];
    }

    // Test browser info function
    $browser_info = gotham_get_browser_info('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    if (!isset($browser_info['name']) || !isset($browser_info['version'])) {
        return ['status' => 'error', 'message' => 'Browser info function failed'];
    }

    return ['status' => 'success', 'message' => 'Enhanced event tracking successful', 'id' => $result, 'user_type' => $user_type, 'browser' => $browser_info['name']];
}

/**
 * Test analytics query functions
 */
function test_gotham_analytics_queries() {
    if (!class_exists('Gotham_Analytics_Admin')) {
        return ['status' => 'error', 'message' => 'Analytics admin class not found'];
    }

    $analytics = new Gotham_Analytics_Admin();

    // Test if methods exist
    $required_methods = ['get_summary_stats', 'get_time_series_data', 'get_conversion_funnel'];
    $missing_methods = [];

    foreach ($required_methods as $method) {
        if (!method_exists($analytics, $method)) {
            $missing_methods[] = $method;
        }
    }

    if (!empty($missing_methods)) {
        return ['status' => 'error', 'message' => 'Missing analytics methods: ' . implode(', ', $missing_methods)];
    }

    return ['status' => 'success', 'message' => 'Analytics query functions are available'];
}

/**
 * Test session management
 */
function test_gotham_session_management() {
    // Test session ID generation
    $session_id = gotham_get_session_id();

    if (empty($session_id)) {
        return ['status' => 'error', 'message' => 'Session ID generation failed'];
    }

    if (!preg_match('/^gs_[a-f0-9]+_[0-9]+$/', $session_id)) {
        return ['status' => 'error', 'message' => 'Session ID format is incorrect'];
    }

    return ['status' => 'success', 'message' => 'Session management working correctly', 'session_id' => $session_id];
}

/**
 * Test bot detection settings
 */
function test_gotham_bot_detection_settings() {
    $settings = gotham_get_bot_detection_settings();

    if (!is_array($settings)) {
        return ['status' => 'error', 'message' => 'Bot detection settings not available'];
    }

    $required_settings = [
        'enabled', 'min_mouse_movements', 'min_interactions', 'grace_period',
        'session_timeout', 'sensitivity', 'exclude_from_analytics'
    ];

    $missing_settings = [];
    foreach ($required_settings as $setting) {
        if (!isset($settings[$setting])) {
            $missing_settings[] = $setting;
        }
    }

    if (!empty($missing_settings)) {
        return ['status' => 'error', 'message' => 'Missing bot detection settings: ' . implode(', ', $missing_settings)];
    }

    return ['status' => 'success', 'message' => 'Bot detection settings configured correctly', 'settings' => $settings];
}

/**
 * Test bot classification engine
 */
function test_gotham_bot_classification() {
    // Test server-side classification with mock data
    $behavioral_data = [
        'mouse_movements' => 0,
        'mouse_clicks' => 0,
        'scroll_events' => 0,
        'keyboard_events' => 0,
        'focus_events' => 0,
        'page_focus_time' => 0,
        'session_duration' => 30000,
        'interaction_patterns' => '[]',
        'behavioral_flags' => '["no_interactions"]'
    ];

    $settings = gotham_get_bot_detection_settings();
    $classification = gotham_server_side_bot_classification($behavioral_data, $settings);

    if (!isset($classification['classification']) || !isset($classification['score'])) {
        return ['status' => 'error', 'message' => 'Bot classification function failed'];
    }

    // Test with zero interactions (should be classified as bot)
    if ($classification['classification'] !== 'bot') {
        return ['status' => 'warning', 'message' => 'Bot classification may be too lenient for zero interactions'];
    }

    return ['status' => 'success', 'message' => 'Bot classification engine working correctly', 'classification' => $classification];
}

/**
 * Test bot detection settings
 */
function test_gotham_bot_detection_settings() {
    $settings = gotham_get_bot_detection_settings();

    if (!is_array($settings)) {
        return ['status' => 'error', 'message' => 'Bot detection settings not available'];
    }

    $required_settings = [
        'enabled', 'min_mouse_movements', 'min_interactions', 'grace_period',
        'session_timeout', 'sensitivity', 'exclude_from_analytics'
    ];

    $missing_settings = [];
    foreach ($required_settings as $setting) {
        if (!isset($settings[$setting])) {
            $missing_settings[] = $setting;
        }
    }

    if (!empty($missing_settings)) {
        return ['status' => 'error', 'message' => 'Missing bot detection settings: ' . implode(', ', $missing_settings)];
    }

    return ['status' => 'success', 'message' => 'Bot detection settings configured correctly', 'settings' => $settings];
}

/**
 * Test bot classification engine
 */
function test_gotham_bot_classification() {
    // Test server-side classification with mock data
    $behavioral_data = [
        'mouse_movements' => 0,
        'mouse_clicks' => 0,
        'scroll_events' => 0,
        'keyboard_events' => 0,
        'focus_events' => 0,
        'page_focus_time' => 0,
        'session_duration' => 30000,
        'interaction_patterns' => '[]',
        'behavioral_flags' => '["no_interactions"]'
    ];

    $settings = gotham_get_bot_detection_settings();
    $classification = gotham_server_side_bot_classification($behavioral_data, $settings);

    if (!isset($classification['classification']) || !isset($classification['score'])) {
        return ['status' => 'error', 'message' => 'Bot classification function failed'];
    }

    // Test with zero interactions (should be classified as bot)
    if ($classification['classification'] !== 'bot') {
        return ['status' => 'warning', 'message' => 'Bot classification may be too lenient for zero interactions'];
    }

    return ['status' => 'success', 'message' => 'Bot classification engine working correctly', 'classification' => $classification];
}

/**
 * Test bot detection database schema
 */
function test_gotham_bot_detection_database() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_adblock_stats';

    // Check if bot detection columns exist
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $bot_columns = [
        'bot_classification', 'bot_score', 'mouse_movements', 'mouse_clicks',
        'scroll_events', 'keyboard_events', 'focus_events', 'page_focus_time',
        'interaction_patterns', 'behavioral_flags', 'classification_timestamp', 'grace_period_active'
    ];

    $existing_columns = array_column($columns, 'Field');
    $missing_columns = array_diff($bot_columns, $existing_columns);

    if (!empty($missing_columns)) {
        return ['status' => 'error', 'message' => 'Missing bot detection columns: ' . implode(', ', $missing_columns)];
    }

    // Check indexes
    $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
    $bot_indexes = ['bot_classification', 'bot_score', 'session_bot'];
    $existing_indexes = array_unique(array_column($indexes, 'Key_name'));
    $missing_indexes = array_diff($bot_indexes, $existing_indexes);

    if (!empty($missing_indexes)) {
        return ['status' => 'warning', 'message' => 'Missing bot detection indexes: ' . implode(', ', $missing_indexes)];
    }

    return ['status' => 'success', 'message' => 'Bot detection database schema is correct'];
}

/**
 * Test behavioral data processing
 */
function test_gotham_behavioral_data_processing() {
    // Test the behavioral data processing function
    $ip = '127.0.0.1';
    $user_agent = 'Test User Agent';
    $behavioral_data = [
        'mouse_movements' => 5,
        'mouse_clicks' => 2,
        'scroll_events' => 3,
        'keyboard_events' => 1,
        'focus_events' => 2,
        'page_focus_time' => 15000,
        'session_duration' => 30000,
        'interaction_patterns' => '[]',
        'behavioral_flags' => '[]',
        'event_type' => 'test_classification'
    ];

    $classification_data = [
        'score' => 25.0,
        'classification' => 'human',
        'confidence' => 0.5
    ];

    $result = gotham_process_bot_classification($ip, $user_agent, $behavioral_data, $classification_data);

    if (!$result) {
        return ['status' => 'error', 'message' => 'Behavioral data processing failed'];
    }

    return ['status' => 'success', 'message' => 'Behavioral data processing working correctly', 'result' => $result];
}



/**
 * Test behavioral data processing
 */
function test_gotham_behavioral_data_processing() {
    // Test the behavioral data processing function
    $ip = '127.0.0.1';
    $user_agent = 'Test User Agent';
    $behavioral_data = [
        'mouse_movements' => 5,
        'mouse_clicks' => 2,
        'scroll_events' => 3,
        'keyboard_events' => 1,
        'focus_events' => 2,
        'page_focus_time' => 15000,
        'session_duration' => 30000,
        'interaction_patterns' => '[]',
        'behavioral_flags' => '[]',
        'event_type' => 'test_classification'
    ];

    $classification_data = [
        'score' => 25.0,
        'classification' => 'human',
        'confidence' => 0.5
    ];

    $result = gotham_process_bot_classification($ip, $user_agent, $behavioral_data, $classification_data);

    if (!$result) {
        return ['status' => 'error', 'message' => 'Behavioral data processing failed'];
    }

    return ['status' => 'success', 'message' => 'Behavioral data processing working correctly', 'result' => $result];
}

/**
 * Run all tests
 */
function run_gotham_tests() {
    $tests = [
        'Enhanced Database Table' => test_gotham_database_table(),
        'Browser Detection' => test_gotham_browser_detection(),
        'OS Detection' => test_gotham_os_detection(),
        'Enhanced Event Tracking' => test_gotham_tracking(),
        'Analytics Query Functions' => test_gotham_analytics_queries(),
        'Session Management' => test_gotham_session_management(),
        'Bot Detection Settings' => test_gotham_bot_detection_settings(),
        'Bot Classification Engine' => test_gotham_bot_classification(),
        'Bot Detection Database' => test_gotham_bot_detection_database(),
        'Behavioral Data Processing' => test_gotham_behavioral_data_processing()
    ];

    return $tests;
}

// Only run tests if this is an admin request and user has proper capabilities
if (is_admin() && current_user_can('manage_options') && isset($_GET['gotham_test'])) {
    $test_results = run_gotham_tests();
    
    echo '<div class="wrap">';
    echo '<h1>Gotham Block Plugin Test Results</h1>';
    
    foreach ($test_results as $test_name => $result) {
        echo '<h2>' . esc_html($test_name) . '</h2>';
        
        if (is_array($result) && isset($result['status'])) {
            $status_class = $result['status'] === 'success' ? 'notice-success' : 'notice-error';
            echo '<div class="notice ' . $status_class . '"><p>' . esc_html($result['message']) . '</p></div>';
        } elseif (is_array($result)) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>User Agent</th><th>Expected</th><th>Detected</th><th>Status</th></tr></thead>';
            echo '<tbody>';
            foreach ($result as $test_case) {
                $status_class = $test_case['status'] === 'pass' ? 'success' : 'error';
                echo '<tr>';
                echo '<td>' . esc_html($test_case['user_agent']) . '</td>';
                echo '<td>' . esc_html($test_case['expected']) . '</td>';
                echo '<td>' . esc_html($test_case['detected']) . '</td>';
                echo '<td><span style="color: ' . ($test_case['status'] === 'pass' ? 'green' : 'red') . '">' . esc_html($test_case['status']) . '</span></td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
        }
    }
    
    echo '</div>';
}
