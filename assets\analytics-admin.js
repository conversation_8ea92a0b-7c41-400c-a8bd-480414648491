// analytics-admin.js - Modern dashboard rendering

document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('gotham-analytics-dashboard')) return;
    fetch(gothamAnalyticsAjax.ajaxurl + '?action=gotham_adblock_get_stats&_wpnonce=' + gothamAnalyticsAjax.nonce)
        .then(res => res.json())
        .then(data => renderDashboard(data));
});

function renderDashboard(data) {
    const dash = document.getElementById('gotham-analytics-dashboard');
    // Always show the graph, even if no data
    dash.innerHTML = `
        <div class="gotham-analytics-card">
            <div class="gotham-analytics-title">Adblock Popup & Conversion</div>
            <div id="mainChart" class="gotham-analytics-chart"></div>
        </div>
        <div class="gotham-analytics-card">
            <div class="gotham-analytics-title">Top Countries</div>
            <div id="countryMap" class="gotham-analytics-chart"></div>
        </div>
        <div class="gotham-analytics-card">
            <div class="gotham-analytics-title">Browser Breakdown</div>
            <div id="browserPie" class="gotham-analytics-chart"></div>
        </div>
    `;
    // Main chart
    new ApexCharts(document.querySelector('#mainChart'), {
        chart: { type: 'line', height: 320 },
        series: [{
            name: 'Popup Displayed',
            data: [data.pop_displayed || 0]
        }, {
            name: 'Adblock Disabled',
            data: [data.adblock_disabled || 0]
        }],
        xaxis: { categories: ['Total'] },
        colors: ['#ff9800', '#4caf50']
    }).render();
    // Country map
    new jsVectorMap({
        selector: '#countryMap',
        map: 'world',
        backgroundColor: '#f8f9fb',
        regionStyle: { initial: { fill: '#e0e7ef' } },
        series: {
            regions: [{
                values: data.countries || {},
                scale: ['#b3c6f7', '#3f51b5'],
                normalizeFunction: 'polynomial'
            }]
        }
    });
    // Browser pie
    new ApexCharts(document.querySelector('#browserPie'), {
        chart: { type: 'pie', height: 320 },
        series: Object.values(data.browsers || {Unknown: 0}),
        labels: Object.keys(data.browsers || {Unknown: 0}),
        colors: ['#4caf50', '#ff9800', '#2196f3', '#9c27b0', '#607d8b', '#f44336', '#00bcd4']
    }).render();
}
