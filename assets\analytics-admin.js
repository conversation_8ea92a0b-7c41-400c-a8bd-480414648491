// analytics-admin.js - Modern dashboard rendering

document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('gotham-analytics-dashboard')) return;
    fetch(gothamAnalyticsAjax.ajaxurl + '?action=gotham_adblock_get_stats&_wpnonce=' + gothamAnalyticsAjax.nonce)
        .then(res => res.json())
        .then(data => renderDashboard(data));
});

function renderDashboard(data) {
    const dash = document.getElementById('gotham-analytics-dashboard');
    // Enhanced dashboard with more detailed stats
    dash.innerHTML = `
        <div class="gotham-analytics-card">
            <div class="gotham-analytics-title">Adblock Detection Overview</div>
            <div class="gotham-stats-grid">
                <div class="stat-item">
                    <div class="stat-number">${data.pop_displayed || 0}</div>
                    <div class="stat-label">Unique Users Detected</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${data.adblock_disabled || 0}</div>
                    <div class="stat-label">Adblock Disabled</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${data.conversion_rate || 0}%</div>
                    <div class="stat-label">Conversion Rate</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${data.popup_closed || 0}</div>
                    <div class="stat-label">Popup Closed</div>
                </div>
            </div>
            <div id="mainChart" class="gotham-analytics-chart"></div>
        </div>
        <div class="gotham-analytics-card">
            <div class="gotham-analytics-title">Top Countries</div>
            <div id="countryMap" class="gotham-analytics-chart"></div>
        </div>
        <div class="gotham-analytics-card">
            <div class="gotham-analytics-title">Browser Breakdown</div>
            <div id="browserPie" class="gotham-analytics-chart"></div>
        </div>
    `;

    // Add CSS for stats grid
    if (!document.getElementById('gotham-stats-css')) {
        const style = document.createElement('style');
        style.id = 'gotham-stats-css';
        style.textContent = `
            .gotham-stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px; }
            .stat-item { text-align: center; padding: 15px; background: #f8f9fb; border-radius: 8px; }
            .stat-number { font-size: 2em; font-weight: bold; color: #3f51b5; }
            .stat-label { font-size: 0.9em; color: #666; margin-top: 5px; }
            @media (max-width: 768px) { .gotham-stats-grid { grid-template-columns: repeat(2, 1fr); } }
        `;
        document.head.appendChild(style);
    }

    // Main chart with better data
    new ApexCharts(document.querySelector('#mainChart'), {
        chart: { type: 'bar', height: 280 },
        series: [{
            name: 'Users',
            data: [
                data.pop_displayed || 0,
                data.adblock_disabled || 0,
                data.popup_closed || 0
            ]
        }],
        xaxis: { categories: ['Detected', 'Converted', 'Declined'] },
        colors: ['#ff9800', '#4caf50', '#f44336'],
        plotOptions: {
            bar: { horizontal: false, columnWidth: '55%' }
        },
        dataLabels: { enabled: true },
        title: { text: 'User Interaction Summary', align: 'center' }
    }).render();

    // Country map (only if we have country data)
    if (data.countries && Object.keys(data.countries).length > 0) {
        new jsVectorMap({
            selector: '#countryMap',
            map: 'world',
            backgroundColor: '#f8f9fb',
            regionStyle: { initial: { fill: '#e0e7ef' } },
            series: {
                regions: [{
                    values: data.countries,
                    scale: ['#b3c6f7', '#3f51b5'],
                    normalizeFunction: 'polynomial'
                }]
            }
        });
    } else {
        document.getElementById('countryMap').innerHTML = '<p style="text-align: center; padding: 50px; color: #666;">No country data available yet</p>';
    }

    // Browser pie (only if we have browser data)
    if (data.browsers && Object.keys(data.browsers).length > 0) {
        new ApexCharts(document.querySelector('#browserPie'), {
            chart: { type: 'pie', height: 320 },
            series: Object.values(data.browsers),
            labels: Object.keys(data.browsers),
            colors: ['#4caf50', '#ff9800', '#2196f3', '#9c27b0', '#607d8b', '#f44336', '#00bcd4'],
            legend: { position: 'bottom' }
        }).render();
    } else {
        document.getElementById('browserPie').innerHTML = '<p style="text-align: center; padding: 50px; color: #666;">No browser data available yet</p>';
    }
}
