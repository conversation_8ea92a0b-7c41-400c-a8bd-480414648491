// analytics-admin.js - Enhanced dashboard with time-series analytics

class GothamAnalyticsDashboard {
    constructor() {
        this.currentPeriod = 'daily';
        this.currentDays = 30;
        this.charts = {};
        this.init();
    }

    init() {
        if (!document.getElementById('gotham-analytics-dashboard')) return;

        this.setupEventListeners();
        this.loadData();
    }

    setupEventListeners() {
        const periodSelector = document.getElementById('period-selector');
        const daysSelector = document.getElementById('days-selector');
        const refreshButton = document.getElementById('refresh-data');

        if (periodSelector) {
            periodSelector.addEventListener('change', (e) => {
                this.currentPeriod = e.target.value;
                this.loadData();
            });
        }

        if (daysSelector) {
            daysSelector.addEventListener('change', (e) => {
                this.currentDays = parseInt(e.target.value);
                this.loadData();
            });
        }

        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.loadData();
            });
        }
    }

    loadData() {
        this.showLoading();

        const params = new URLSearchParams({
            action: 'gotham_adblock_get_stats',
            period: this.currentPeriod,
            days: this.currentDays,
            _wpnonce: gothamAnalyticsAjax.nonce
        });

        fetch(gothamAnalyticsAjax.ajaxurl + '?' + params)
            .then(res => res.json())
            .then(data => {
                this.hideLoading();
                if (data.success === false) {
                    this.showError(data.data?.message || 'Failed to load data');
                } else {
                    this.renderDashboard(data);
                }
            })
            .catch(error => {
                this.hideLoading();
                this.showError('Network error: ' + error.message);
            });
    }

    showLoading() {
        document.getElementById('loading-indicator').style.display = 'block';
        document.getElementById('error-message').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loading-indicator').style.display = 'none';
    }

    showError(message) {
        const errorDiv = document.getElementById('error-message');
        errorDiv.querySelector('p').textContent = message;
        errorDiv.style.display = 'block';
    }
}

    renderDashboard(data) {
        this.renderSummaryCards(data.summary);
        this.renderMainDashboard(data);
    }

    renderSummaryCards(summary) {
        const container = document.getElementById('summary-cards');
        if (!container) return;

        const botStats = summary.bot_stats || {};
        const totalClassified = botStats.total_classified || 0;
        const botDetectionEnabled = totalClassified > 0; // If we have classified users, bot detection is enabled

        if (botDetectionEnabled) {
            // Bot detection enabled - show only verified human users
            container.innerHTML = `
                <div class="summary-card">
                    <div class="metric-value">${summary.total_users || 0}</div>
                    <div class="metric-label">Verified Human Users</div>
                    <div class="metric-subtitle">Only users classified as human</div>
                </div>
                <div class="summary-card conversion">
                    <div class="metric-value">${summary.converted_users || 0}</div>
                    <div class="metric-label">Human Conversions</div>
                    <div class="metric-subtitle">Verified human users only</div>
                </div>
                <div class="summary-card declined">
                    <div class="metric-value">${summary.declined_users || 0}</div>
                    <div class="metric-label">Human Declines</div>
                    <div class="metric-subtitle">Verified human users only</div>
                </div>
                <div class="summary-card rate">
                    <div class="metric-value">${summary.conversion_rate || 0}%</div>
                    <div class="metric-label">Human Conversion Rate</div>
                    <div class="metric-subtitle">Excluding bot traffic</div>
                </div>
                <div class="summary-card bot-detection">
                    <div class="metric-value">${botStats.bot_visitors || 0}</div>
                    <div class="metric-label">Bot Visitors Detected</div>
                    <div class="metric-subtitle">Avg score: ${botStats.avg_bot_score || 0}</div>
                </div>
            `;
        } else {
            // Bot detection disabled - show all users (legacy behavior)
            container.innerHTML = `
                <div class="summary-card">
                    <div class="metric-value">${summary.total_users || 0}</div>
                    <div class="metric-label">Total Users Detected</div>
                    <div class="metric-subtitle">Bot detection disabled</div>
                </div>
                <div class="summary-card conversion">
                    <div class="metric-value">${summary.converted_users || 0}</div>
                    <div class="metric-label">Adblock Disabled</div>
                    <div class="metric-subtitle">All users included</div>
                </div>
                <div class="summary-card declined">
                    <div class="metric-value">${summary.declined_users || 0}</div>
                    <div class="metric-label">Popup Declined</div>
                    <div class="metric-subtitle">All users included</div>
                </div>
                <div class="summary-card rate">
                    <div class="metric-value">${summary.conversion_rate || 0}%</div>
                    <div class="metric-label">Conversion Rate</div>
                    <div class="metric-subtitle">All traffic included</div>
                </div>
                <div class="summary-card bot-detection">
                    <div class="metric-value">N/A</div>
                    <div class="metric-label">Bot Detection</div>
                    <div class="metric-subtitle">Disabled</div>
                </div>
            `;
        }
    }

    renderMainDashboard(data) {
        const container = document.getElementById('gotham-analytics-dashboard');
        if (!container) return;

        container.innerHTML = `
            <div class="gotham-analytics-card">
                <div class="gotham-analytics-title">User Behavior Over Time</div>
                <div id="timeSeriesChart" class="gotham-analytics-chart"></div>
            </div>
            <div class="gotham-analytics-card">
                <div class="gotham-analytics-title">Bot Detection Analytics</div>
                <div id="botDetectionChart" class="gotham-analytics-chart chart-small"></div>
            </div>
            <div class="gotham-analytics-card">
                <div class="gotham-analytics-title">Conversion Funnel</div>
                <div id="conversionFunnel" class="gotham-analytics-chart chart-small"></div>
            </div>
            <div class="gotham-analytics-card">
                <div class="gotham-analytics-title">Geographic Distribution</div>
                <div id="geographicChart" class="gotham-analytics-chart chart-small"></div>
            </div>
            <div class="gotham-analytics-card">
                <div class="gotham-analytics-title">Browser & Device Analytics</div>
                <div id="browserChart" class="gotham-analytics-chart chart-small"></div>
            </div>
        `;

        this.renderTimeSeriesChart(data.time_series);
        this.renderBotDetectionChart(data.summary.bot_stats);
        this.renderConversionFunnel(data.funnel);
        this.renderGeographicChart(data.geographic);
        this.renderBrowserChart(data.browsers);
    }

    renderTimeSeriesChart(timeSeriesData) {
        if (!timeSeriesData || timeSeriesData.length === 0) {
            document.getElementById('timeSeriesChart').innerHTML =
                '<p style="text-align: center; padding: 50px; color: #666;">No time series data available</p>';
            return;
        }

        const categories = timeSeriesData.map(item => {
            if (this.currentPeriod === 'hourly') {
                return new Date(item.time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            } else if (this.currentPeriod === 'weekly') {
                return `Week ${item.time}`;
            } else {
                return new Date(item.time).toLocaleDateString();
            }
        });

        const popupData = timeSeriesData.map(item => item.pop_displayed || 0);
        const convertedData = timeSeriesData.map(item => item.adblock_disabled || 0);
        const declinedData = timeSeriesData.map(item => item.popup_closed || 0);

        const options = {
            chart: {
                type: 'line',
                height: 400,
                toolbar: { show: true }
            },
            series: [
                { name: 'Popup Displayed', data: popupData },
                { name: 'Adblock Disabled', data: convertedData },
                { name: 'Popup Declined', data: declinedData }
            ],
            xaxis: {
                categories: categories,
                title: { text: this.getPeriodLabel() }
            },
            yaxis: {
                title: { text: 'Number of Users' }
            },
            colors: ['#3f51b5', '#4caf50', '#f44336'],
            stroke: { width: 3, curve: 'smooth' },
            markers: { size: 5 },
            legend: { position: 'top' },
            tooltip: {
                shared: true,
                intersect: false
            }
        };

        if (this.charts.timeSeries) {
            this.charts.timeSeries.destroy();
        }
        this.charts.timeSeries = new ApexCharts(document.querySelector('#timeSeriesChart'), options);
        this.charts.timeSeries.render();
    }

    getPeriodLabel() {
        switch (this.currentPeriod) {
            case 'hourly': return 'Hour';
            case 'weekly': return 'Week';
            default: return 'Date';
        }
    }

    renderBotDetectionChart(botStats) {
        const container = document.getElementById('botDetectionChart');
        if (!container) return;

        if (!botStats || botStats.total_classified === 0) {
            container.innerHTML = '<p style="text-align: center; padding: 50px; color: #666;">No bot detection data available</p>';
            return;
        }

        // Create a mixed chart showing bot vs human traffic
        const options = {
            chart: {
                type: 'donut',
                height: 300
            },
            series: [botStats.human_visitors || 0, botStats.bot_visitors || 0],
            labels: ['Human Visitors', 'Bot Visitors'],
            colors: ['#4caf50', '#f44336'],
            legend: { position: 'bottom' },
            plotOptions: {
                pie: {
                    donut: {
                        size: '60%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total Classified',
                                formatter: function() {
                                    return botStats.total_classified || 0;
                                }
                            }
                        }
                    }
                }
            },
            tooltip: {
                formatter: function() {
                    const percentage = ((this.y / botStats.total_classified) * 100).toFixed(1);
                    return `<b>${this.key}</b><br/>${this.y} visitors (${percentage}%)`;
                }
            }
        };

        if (this.charts.botDetection) {
            this.charts.botDetection.destroy();
        }
        this.charts.botDetection = new ApexCharts(container, options);
        this.charts.botDetection.render();
    }

    renderConversionFunnel(funnelData) {
        const container = document.getElementById('conversionFunnel');
        if (!container) return;

        if (!funnelData) {
            container.innerHTML = '<p style="text-align: center; padding: 50px; color: #666;">No funnel data available</p>';
            return;
        }

        container.innerHTML = `
            <div class="funnel-container">
                <div class="funnel-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">Users Detected</div>
                        <div class="step-value">${funnelData.popup_displayed || 0}</div>
                        <div class="step-percentage">100%</div>
                    </div>
                </div>
                <div class="funnel-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">Users Interacted</div>
                        <div class="step-value">${funnelData.interacted || 0}</div>
                        <div class="step-percentage">${funnelData.interaction_rate || 0}%</div>
                    </div>
                </div>
                <div class="funnel-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">Users Converted</div>
                        <div class="step-value">${funnelData.converted || 0}</div>
                        <div class="step-percentage">${funnelData.conversion_rate || 0}%</div>
                    </div>
                </div>
            </div>
        `;
    }

    renderGeographicChart(geographicData) {
        const container = document.getElementById('geographicChart');
        if (!container) return;

        if (!geographicData || Object.keys(geographicData).length === 0) {
            container.innerHTML = '<p style="text-align: center; padding: 50px; color: #666;">No geographic data available</p>';
            return;
        }

        const countries = Object.keys(geographicData).slice(0, 10);
        const userData = countries.map(country => geographicData[country].users);
        const conversionData = countries.map(country => geographicData[country].conversions);

        const options = {
            chart: {
                type: 'bar',
                height: 300,
                stacked: false
            },
            series: [
                { name: 'Total Users', data: userData },
                { name: 'Conversions', data: conversionData }
            ],
            xaxis: {
                categories: countries,
                labels: { rotate: -45 }
            },
            colors: ['#3f51b5', '#4caf50'],
            plotOptions: {
                bar: { columnWidth: '60%' }
            },
            legend: { position: 'top' }
        };

        if (this.charts.geographic) {
            this.charts.geographic.destroy();
        }
        this.charts.geographic = new ApexCharts(container, options);
        this.charts.geographic.render();
    }

    renderBrowserChart(browserData) {
        const container = document.getElementById('browserChart');
        if (!container) return;

        if (!browserData || Object.keys(browserData).length === 0) {
            container.innerHTML = '<p style="text-align: center; padding: 50px; color: #666;">No browser data available</p>';
            return;
        }

        const browsers = Object.keys(browserData).slice(0, 8);
        const userData = browsers.map(browser => browserData[browser].users);

        const options = {
            chart: {
                type: 'donut',
                height: 300
            },
            series: userData,
            labels: browsers,
            colors: ['#3f51b5', '#4caf50', '#ff9800', '#f44336', '#9c27b0', '#607d8b', '#00bcd4', '#795548'],
            legend: { position: 'bottom' },
            plotOptions: {
                pie: {
                    donut: {
                        size: '60%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total Users'
                            }
                        }
                    }
                }
            }
        };

        if (this.charts.browser) {
            this.charts.browser.destroy();
        }
        this.charts.browser = new ApexCharts(container, options);
        this.charts.browser.render();
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new GothamAnalyticsDashboard();
});
