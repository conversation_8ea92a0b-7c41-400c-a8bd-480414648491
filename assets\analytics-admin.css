/* analytics-admin.css - Modern dashboard styles */
.gotham-analytics-admin { font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif; background: #f8f9fb; padding: 30px; }
#gotham-analytics-dashboard { display: grid; grid-template-columns: 2fr 1fr; grid-gap: 32px; }
.gotham-analytics-card { background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 24px; margin-bottom: 24px; }
.gotham-analytics-title { font-size: 1.2em; font-weight: 600; margin-bottom: 16px; }
.gotham-analytics-chart { width: 100% !important; height: 320px !important; }
.gotham-analytics-empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 320px;
  color: #888;
  font-size: 1.2em;
  background: #f8f9fb; /* match dashboard background */
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  margin: 32px 0;
}
.gotham-analytics-empty-message svg {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}
@media (max-width: 900px) { #gotham-analytics-dashboard { grid-template-columns: 1fr; } }
