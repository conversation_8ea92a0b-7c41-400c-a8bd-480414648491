<?php
/**
 * @package Gotham Block Extra Light
 * @version 1.5.0
 */
/*
Plugin Name: Gotham Block Extra Light
Description: 🇬🇧 ULTRA Light plugin to inform your visitors that ad blockers are killing the viability of your site, and invite them to deactivate them 🇫🇷 Plugin ULTRA Léger pour informer tout simplement vos visiteurs que les bloqueurs de publicité tuent la viabilité de votre site, et les invite à les désactiver.
Version: 1.5.0
Author: Kapsule Network
Author URI: https://www.kapsulecorp.com/
License: GPLv2
*/

if ( ! defined( 'ABSPATH' ) )
	exit;

define('GOTHAMBKOCKADBLOCK_ROOTPATH', plugin_dir_path( __FILE__ )); // Chemin Serveur

//////////////////////
// Check PREMIUM //
$needpremium = get_option('gothamadblock_option_premium_tools');
require_once(GOTHAMBKOCKADBLOCK_ROOTPATH.'/premium/valid_api.php');
// Si on a besoin de fonctions PREMIUM => Test de la Validité de la licence //

if ($needpremium == "oui") {
	
	$ghostpremium_doubleface = false;
	$ghostpremium_doubleface = check_kapsuleapi_ghostpremium_licence();

	if ($ghostpremium_doubleface == "cUrl") {
		
		$msgerreur = "Votre serveur n'arrive pas à se connecter à notre API - cURL error 28: Operation timed out - L'erreur semble venir de votre solution d'hébergement car tout fonctionne du côté des serveurs de Gotham Block Adblock";
		$ghostpremium_doubleface = false;
		
	} elseif ($ghostpremium_doubleface == "Erreur") {
		
		$msgerreur = "Votre serveur n'arrive pas à se connecter à notre API"; 
		$ghostpremium_doubleface=false;
		
	 } else {
		 
		 $msgerreur = "";
		 
	}

	define('KINGBOO', $ghostpremium_doubleface); // API ACTIVE ou NON
	
} else {
	
	define('KINGBOO', false);
	
}
// ! Check PREMIUM //
///////////////////////////

// Est-on en pause ?
$gothamadblock_option_fury = get_option('gothamadblock_option_fury');

if ( ($gothamadblock_option_fury != "paused") AND (!is_admin()) ){ // Si on est pas en pause // Ni en mode admin (ce qui créé un confit avec les nouveaux block widgets)
	
	// Est-on en mode FURY ou non ?
	if (($gothamadblock_option_fury == "ssj2") OR ($gothamadblock_option_fury == "ssj3")) { $agressive_mode = true;} else {$agressive_mode = false;}
	//////////////////////////////

	if ((!isset($_COOKIE['gothamadblock_last_visit_time'])) OR ($agressive_mode == true)) { // Si Pas de Cookie ou Si Mode Agressif activé on affiche la Popup

	// On ajoute le Honey Pot

		/*
		////////////////////////////////////////////////////
		Version avec Ads.js////////////////////////////////
		//////////////////////////////////////////////////
		
		function gothamadblock_register_my_scripts() {
			wp_register_script( 'gothamhoneypot', plugins_url( '/ads.js', __FILE__ ) );
			wp_enqueue_script( 'gothamhoneypot' );
		}
		add_action( 'wp_enqueue_scripts', 'gothamadblock_register_my_scripts', 0 );*/
		
		// Popup si Honeypot bloqué par un Adblocker
		function gothamadblock_mapop() {
			$defopiks = plugin_dir_url( __FILE__ ).'stop.png';
			$igotit = plugin_dir_url( __FILE__ ).'ok.png';
			$nonce = wp_create_nonce('gotham_adblock_nonce');
			
			// Track popup display
			gotham_adblock_track_event('pop_displayed', 'pending');
			
			echo '<script>
			function gothamadblock_myClosePop() {
				var mes = document.getElementById("gothamadblock_msg");
				var over = document.getElementById("gothamadblock_overlayh_n");
				mes.style.display = "none";
				over.style.display = "none";
				document.body.classList.remove("gtmab_leviator");
				
				// Track that user closed without disabling adblock
				jQuery.ajax({
					url: ajaxurl,
					type: "POST",
					data: {
						action: 'gotham_track_adblock_action',
						event_type: 'popup_closed',
						status: 'declined',
						nonce: "' . $nonce . '"
					}
				});
			}
			
			function gothamadblock_myClosePopSSJ() {
				// Track that user disabled adblock
				jQuery.ajax({
					url: ajaxurl,
					type: "POST",
					data: {
						action: 'gotham_track_adblock_action',
						event_type: 'adblock_disabled',
						status: 'accepted',
						nonce: "' . $nonce . '"
					},
					success: function() {
						window.location.reload();
					}
				});
			}
			</script>';


			//////////// Si pas de message personnalisé

							$gothamadblock_option_messageperso_title = get_option('gothamadblock_option_messageperso_title');
							if ($gothamadblock_option_messageperso_title == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												
												$popup_title = "Adblock détecté";
												
											} else {
												
												$popup_title = "Adblock Detected";
												
											}

										//////////// !Zone de Test Langage
							} else {
								
								$popup_title = $gothamadblock_option_messageperso_title;
							
							}

							$gothamadblock_option_messageperso = get_option('gothamadblock_option_messageperso');
							if ($gothamadblock_option_messageperso == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												$mon_texte_sensibilisation = "<p><u>Ce site internet ne peut exister que grâce à la présence de publicité</u>.<br />Merci de <strong>couper votre logiciel Adblock</Strong> sur ce site et de cliquer sur le bouton j'ai compris.</p>";
											}
											else {
												$mon_texte_sensibilisation = "<p><u>This website can only exist thanks to the presence of advertising</u>.<br />Please <strong>deactivate your Adblock</Strong> software on this site and click on the button I understood.</p>";
											}

										//////////// !Zone de Test Langage
							}
							else {
							$mon_texte_sensibilisation = $gothamadblock_option_messageperso;
							$mon_texte_sensibilisation = wpautop($mon_texte_sensibilisation); // Conversion des sauts de ligne pour corriger le bug du saut de ligne
							}

							$gothamadblock_option_messageperso_button = get_option('gothamadblock_option_messageperso_button');
							if ($gothamadblock_option_messageperso_button == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												$popup_ctatext = "J'ai compris";
											}
											else {

												$popup_ctatext = "I Understood";
											}
										//////////// !Zone de Test Langage
							}
							else {
							$popup_ctatext = $gothamadblock_option_messageperso_button;
							}

			// Est-on en SSJ3 ? Si oui la popup recharge la page et donc revérifie si le adblock est bien coupé, sinon cela coupe la popup.
			$gothamadblock_option_fury = get_option('gothamadblock_option_fury');
			if ($gothamadblock_option_fury == "ssj3") {$janemba="gothamadblock_myClosePopSSJ()";} else {$janemba="gothamadblock_myClosePop()";}
			
			// Construction de la Popup
			$cestbononatout_onconstruit = "<div id='gothamadblock_msg' style='display:block;'><h2>$popup_title</h2><img src='$defopiks' alt='Oing' height='300' width='300' />$mon_texte_sensibilisation<button id='gtab_mehn' onclick='$janemba'>$popup_ctatext</button></div><div id='gothamadblock_overlayh_n' style='display:block;'></div>"; 
			$cestbononatout_onconstruit = str_replace(array("\n", "\r\n", "\r", "\t", "    "), "", $cestbononatout_onconstruit); // On vire tous les sauts de ligne

			
			 // Si bloqué on affiche la popup en JS
			 /* Mécanisme inspiré d'un script de AdGlare Ad Server */
			echo "
			<script>
				function gothamBatAdblock() {
					var a = document.createElement('div');
					a.innerHTML = '&nbsp;';
					a.className = 'gothamads publicite 300x250 text-ad text_ad text_ads text-ads pub_728x90 textAd text-ad-links adsbox moneytizer';
					a.style = 'position: absolute !important; width: 0!important; height: 1px !important; left: -1000px !important; top: -10000px !important;';
					var r = false;
					try {
						document.body.appendChild(a);
						var e = document.getElementsByClassName('gothamads')[0];
						if(e.offsetHeight === 0 || e.clientHeight === 0) r = true;
						if(window.getComputedStyle !== undefined) {
							var tmp = window.getComputedStyle(e, null);
							if(tmp && (tmp.getPropertyValue('display') == 'none' || tmp.getPropertyValue('visibility') == 'hidden')) r = true;
						}
						document.body.removeChild(a);
					} catch (e) {}
					return r;
				}
		   if(gothamBatAdblock()) {
		   document.write(\"$cestbononatout_onconstruit\");
			document.body.classList.add('gtmab_leviator');
		  } 
			</script>";

			  // Si bloqué on affiche le CSS
			echo "<style type='text/css'>
				.gtmab_leviator {height:100%;overflow:hidden;}
				#gothamadblock_msg{position:fixed;width:800px;margin:0 auto;background:#fff;height:auto;display:block;float:left;z-index:99999999;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%);border-radius:8px;border:4px solid orange;padding:40px 0!important;}#gothamadblock_msg img{width:150px;height:150px;margin:20px auto!important;clear:both}#gothamadblock_msg h2{font-weight:700!important;font-family:arial!important;padding:10px 0!important;font-size:26px!important;}#gothamadblock_msg p{margin:30px 0!important;}button#gtab_mehn {cursor:pointer;display: inline-block;text-align: center;vertical-align: middle;padding: 12px 24px;border: 1px solid #4443cf;border-radius: 8px;background: #807eff;background: -webkit-gradient(linear, left top, left bottom, from(#807eff), to(#4443cf));background: -moz-linear-gradient(top, #807eff, #4443cf);background: linear-gradient(to bottom, #807eff, #4443cf);font: normal normal bold 20px arial;color: #ffffff;text-decoration: none;}button#gtab_mehn:focus,button#gtab_mehn:hover{border:1px solid ##504ff4;background:#9a97ff;background:-webkit-gradient(linear,left top,left bottom,from(#9a97ff),to(#5250f8));background:-moz-linear-gradient(top,#9a97ff,#5250f8);background:linear-gradient(to bottom,#9a97ff,#5250f8);color:#fff;text-decoration:none}button#gtab_mehn:active{background:#4443cf;background:-webkit-gradient(linear,left top,left bottom,from(#4443cf),to(#4443cf));background:-moz-linear-gradient(top,#4443cf,#4443cf);background:linear-gradient(to bottom,#4443cf,#4443cf)}button#gtab_mehn:before{content:'';display:inline-block;height:24px;width:24px;line-height:24px;margin:0 4px -6px -4px;position:relative;top:0;left:-3px;background:url($igotit) no-repeat left center transparent;background-size:100% 100%}#gothamadblock_overlayh_n{position:fixed;width:100%;margin:0 auto;opacity:.8;background:#000;height:100%;display:block;float:left;z-index:99999998;top:0;}
				@media only screen and (max-width: 1024px){#gothamadblock_msg{position:fixed;width:90%;margin:0 auto;background:#fff;height:auto;display:block;float:left;z-index:99999999;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%);border-radius:8px;border:4px solid orange;padding:10px;}}@media only screen and (max-width: 767px){#gothamadblock_msg img {width:100px;height:100px;}#gothamadblock_msg {padding:10px!important;}}
				</style>";
			}

			// On lance le plugin
			function gothamadblock_gothamkill() {
				$chosen = gothamadblock_mapop();
				}
			add_action( 'wp_footer', 'gothamadblock_gothamkill' );
	}


	// On dépose un cookie 
	add_action( 'init', 'gothamadblock_add_Cookie' );
	function gothamadblock_add_Cookie() {
		if (!isset($_COOKIE['gothamadblock_last_visit_time'])) { // Si un cookie n'est pas déjà en place et le délai afférant entrain de courir
		$tempsdecuisson = get_option('gothamadblock_option_cookietime'); // On récupère la durée voulue
		if ((empty($tempsdecuisson)) OR ($tempsdecuisson=="")) {$tempsdecuisson=2592000;} // Si paramétrage de la durée de vie du cookie vide, on fixe à 30 jours par défaut
		setcookie("gothamadblock_last_visit_time", "1", time()+$tempsdecuisson, "/"); // On pose le cookie
		}
	}
}

//////////////////////////////////////////////////////////////////////////////////////
// Création du Copyrighting
//////////////////////////////////////////////////////////////////////////////////////
$gothamadblock_option_powered_check = get_option('gothamadblock_option_powered');
if ($gothamadblock_option_powered_check == "oui") {
		function gothamadblock_powered_seo() {
			echo "<p style='text-align:center;'>Plugin <a href='https://www.kapsulecorp.com/' target='_blank' rel='noopener'>Kapsule Corp</a></p>";
			}
		add_action( 'wp_footer', 'gothamadblock_powered_seo' );
}

//////////////////////////////////////////////////////////////////////////////////////
// Création du Menu et de l'enregistrement des options
//////////////////////////////////////////////////////////////////////////////////////

// Si c'est l'admin
if ( is_admin() ){

	///////////////////////////////////
	// On créé les options dans le SQL
	///////////////////////////////////

	function gotham_blockadblock_html_sanitize_callback ($string)
	{
		$gotham_blockadblock_allowed_html = array(
		'a' => array(
			'href' => array(),
			'title' => array(),
			'rel' => array(),
			'target' => array()
		),
		'br' => array(),
		'em' => array(),
		'strong' => array(),
		'b' => array(),
		'u' => array(),
		'strike' => array(),
		'h1' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h2' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h3' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h4' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h5' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'p' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'span' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'ul' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'ol' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'li' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		)
		);	
		
		return wp_kses($string,$gotham_blockadblock_allowed_html);
	}

	add_action( 'admin_init', 'gothamadblock_batarang' );
	function gothamadblock_batarang() {
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_fury', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_cookietime', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso_title', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso', 'gotham_blockadblock_html_sanitize_callback' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso_button', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_powered', 'sanitize_text_field' );
		// PREMIUM API KEY
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_premium_tools', 	'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_apijeton', 	'sanitize_text_field' );
		add_action('admin_enqueue_scripts', 'check_licence_update_ghostpremium');
	}

	///////////////////////////////////
	// On créé le menu
	//////////////////////////////////

	add_action('admin_menu','gothamadblock_setupmenu');
	function gothamadblock_setupmenu(){
		  add_menu_page('Configuration de Gotham Block Adblock', 'G BlockAdblock', 'administrator', 'gotham-plugin', 'gothamadblock_init_cave', 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgZW5hYmxlLWJhY2tncm91bmQ9Im5ldyAwIDAgMjQgMjQiIGlkPSJMYXllcl8xIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+PHBhdGggZD0iTTE2LjUsMTYuNSAgYy03LTMtOSwzLTksM2MtNS41LTItNyw0LTcsNGMwLTkuNSw0LTEzLDQtMTNzLTEsMywyLDNzMS45OTk4Njg0LTQuNSwwLTVoMC4yOTI4OTM5ICBjMC40NTI3NTI2LDAsMC44ODY5NjE1LTAuMTc5ODU1MywxLjIwNzEwNTYtMC40OTk5OTlMOC4wMDAwMDEsNy45OTk5OTk1QzguMzIwMTQ0Nyw3LjY3OTg1NTMsOC41LDcuMjQ1NjQ2NSw4LjUsNi43OTI4OTM5VjYuNSAgYzAuNSwxLjk5OTg2ODQsNSwzLDUsMHMtMy0yLTMtMnMzLjUtNCwxMy00YzAsMC02LDEuNS00LDdDMTkuNSw3LjUsMTMuNSw5LjUsMTYuNSwxNi41IiBmaWxsPSJub25lIiBzdHJva2U9IiMzMDNDNDIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLW1pdGVybGltaXQ9IjEwIi8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PC9zdmc+' );
	}

	///////////////////////////////////
	// On charge le JS de l'admin
	//////////////////////////////////

	function gothamadblock_monjsdansladmin() {
	echo "<style>
	.gotham_ad_wrap #logo_admin {text-align:center;background:black;color:#ac5b5b;padding:80px;border-radius:8px;}
	.gotham_ad_wrap{margin: 10px 20px 0 2px;}
	.gotham_ad_form{float: left;width: 79%;}
	.gotham_ad_credit{float: left;width:17%;background:#fff;box-shadow: 0 0 0 1px rgba(0,0,0,0.05);padding:1%;margin-left:1%;}
	#batbaseadmin tr td.libelle{font-weight:bold;width:250px;}
	#batbaseadmin input, #batbaseadmin select, #batbaseadmin textarea {width:280px;float:left;}
	.explain {background:white;box-shadow: 0 0 0 1px rgba(0,0,0,0.05);}
	.explain p{padding:10px;}
	.explain ul{padding: 0 10px;list-style: square inside;}
	.explain li{padding:10px 0;}
	.explain h3 {padding:6px 10px;border-bottom:1px solid #eee;}
	</style>";

	echo "<script>function besoindelacuisiniere(sel) {var gotham_cuisiniere_ssj = sel.value;if (gotham_cuisiniere_ssj == 'ssj1') {document.getElementById('gothamadblock_option_cookietime').style.display = 'block';} else { document.getElementById('gothamadblock_option_cookietime').style.display = 'none';}} </script><script>
		window.onload = function () {
		document.getElementById('need_premium_or_not').addEventListener('change', function () {
			if (this.value == 'oui') {
				document.getElementById('hidou').style.display = 'table-row';
			} else {
				document.getElementById('hidou').style.display = 'none';
			}
		}, false)
		};
		</script>";

	}

	add_action('admin_enqueue_scripts', 'gothamadblock_monjsdansladmin');

	// Création de la page d'options du plugin ////////////////
	function gothamadblock_init_cave(){

	///////////////////////////////////////
	/// Mini zone de langage pour l'admin
	///////////////////////////////////////

	///FRANCAIS
	if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
		$txt_adwin_welcome = " Bienvenue dans la BatBase ";
		$txt_adwin_yes = "Oui";
		$txt_adwin_no = "Non";
		$txt_adwin_ssj1 = "SSJ1 Light";
		$txt_adwin_ssj2 = "SSJ2 Agressive";
		$txt_adwin_ssj3 = "SSJ3 Fury";
		$txt_adwin_paused = "Pause ";
		$txt_adwin_titre = "Titre personnalisé";
		$txt_adwin_corpus = "Texte personnalisé (HTML autorisé)";
		$txt_adwin_cta = "Bouton personnalisé";
		$txt_adwin_firemode = "1. Je choisis le degré d'agressivité du plugin !";
		$txt_adwin_firemode_p = "<p>Dans tous les cas, la popup informera l'internaute que bloquer les publicités tue votre buisness model et l'invitera à désactiver son adblocker. Mais vous pouvez choisir ci-dessous le comportement de la popup en fixant son degré d'agressivité selon 3 niveaux.</p><ul><li> SSJ1 (Choix par défaut) : Le message ne s'affichera qu'une fois tous les x minutes/heures/jours (30 jours par défaut), tant que l'internaute aura un logiciel Adblock activé, mais l'internaute pourra fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)</li><li> SSJ2 : Le message s'affichera à chaque chargement de page, tant que l'internaute aura un logiciel Adblock activé, cependant l'internaute pourra à chaque fois fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)</li><li> SSJ3 : Le message s'affichera à chaque chargement de page, et l'internaute ne pourra pas naviguer sur votre site tant qu'il aura un logiciel Adblock activé. Rends la navigation impossible avec Adblock !</li></ul>";
		$txt_adwin_mecha = "2. Je customise (ou pas) la popup !";
		$txt_adwin_mecha_p = "Saisissez le texte de votre choix ou laissez vide pour afficher le texte par défaut.";
	}
	///ANGLAIS
	else {
		$txt_adwin_welcome = "Welcome to the BatBase ";
		$txt_adwin_yes = "Yes";
		$txt_adwin_no = "No";
		$txt_adwin_ssj1 = "SSJ1 Light";
		$txt_adwin_ssj2 = "SSJ2 Agressive";
		$txt_adwin_ssj3 = "SSJ3 Fury";
		$txt_adwin_paused = "Pause ";
		$txt_adwin_titre = "Customized Headline";
		$txt_adwin_corpus = "Customized message (HTML allowed)";
		$txt_adwin_cta = "Customized Button";
		$txt_adwin_firemode = "1. I choose the degree of aggressiveness of the plugin";
		$txt_adwin_firemode_p = "<p>In any case, the popup will inform the user that blocking ads kills your buisness model and will invite him to deactivate his adblocker. But you can choose below the behavior of the popup by setting its degree of aggressiveness according to 3 levels.</p><ul><li> SSJ1 (Default) : The message will only be displayed once every X minute/hours/days (30 days by default), as long as the user has Adblock software activated, but the user can close the popup and continue browsing your site normally (even with Adblock activated)</li><li> SSJ2 : The message will appear on each page load, as long as the user has Adblock software activated, however the user can each time close the popup and continue browsing your site normally (even with Adblock activated)</li><li> SSJ3 : The message will appear on each page load, and the user will not be able to navigate on your site as long as he has Adblock software activated. This level of aggressiveness makes navigation impossible with Adblock!</li></ul>";
		$txt_adwin_mecha = "2. I customize (or not) the popup";
		$txt_adwin_mecha_p = "Enter your text or leave blank for display the default text";
	}
	////////////////////////////////////////

	?>
	<div class="gotham_ad_wrap">
	  <h1 id="logo_admin"><?php echo $txt_adwin_welcome; ?></h1>

	  <div class="gotham_ad_form">
	  <form method="post" action="options.php">
	  <?php settings_fields( 'gothamadblockbat-settings-group' ); ?>
	  <?php do_settings_sections('gothamadblockbat-settings-group'); ?>


		  <table id="batbaseadmin">
				<tr class="explain">
				<td colspan="2">
			  <h3> <?php echo $txt_adwin_firemode; ?></h3>
			  <?php echo $txt_adwin_firemode_p; ?>
				</td>
				</tr>
			  <tr>
				  <td class="libelle"><label for="gothamadblock_option_fury">Fury Mode :</label></td>
				  <td>
					<?php $gothamadblock_option_fury = get_option('gothamadblock_option_fury'); ?>
					<select id="gothamadblock_option_fury" name="gothamadblock_option_fury" value="<?php echo get_option('gothamadblock_option_fury'); ?>" onchange="besoindelacuisiniere(this)">
						<option value="ssj1" <?php selected( $gothamadblock_option_fury, 'ssj1' ); ?>><?php echo $txt_adwin_ssj1; ?></option>
						<option value="ssj2" <?php selected( $gothamadblock_option_fury, 'ssj2' ); ?>><?php echo $txt_adwin_ssj2; ?></option>
						<option value="ssj3" <?php selected( $gothamadblock_option_fury, 'ssj3' ); ?>><?php echo $txt_adwin_ssj3; ?></option>
						<option value="paused" <?php selected( $gothamadblock_option_fury, 'paused' ); ?>><?php echo $txt_adwin_paused; ?></option>
					</select>
					<?php $gothamadblock_option_cookietime = get_option('gothamadblock_option_cookietime'); ?>
					<select id="gothamadblock_option_cookietime" name="gothamadblock_option_cookietime" value="<?php echo get_option('gothamadblock_option_cookietime'); ?>" <?php if ($gothamadblock_option_fury != "ssj1") { ?>style="display:none;"<?php } ?>>
						<option value="2592000" <?php selected( $gothamadblock_option_cookietime, '2592000' ); ?>>30 <?php echo $txt_adwin_mot_jours; ?> (Default)</option>
						<option value="1296000" <?php selected( $gothamadblock_option_cookietime, '1296000' ); ?>>15 <?php echo $txt_adwin_mot_jours; ?></option>
						<option value="604800" <?php selected( $gothamadblock_option_cookietime, '604800' ); ?>>7 <?php echo $txt_adwin_mot_jours; ?></option>
						<option value="172800" <?php selected( $gothamadblock_option_cookietime, '172800' ); ?>>48H</option>
						<option value="86400" <?php selected( $gothamadblock_option_cookietime, '86400' ); ?>>24H</option>
						<option value="7200" <?php selected( $gothamadblock_option_cookietime, '7200' ); ?>>2H</option>
						<option value="3600" <?php selected( $gothamadblock_option_cookietime, '3600' ); ?>>1H</option>
						<option value="1800" <?php selected( $gothamadblock_option_cookietime, '1800' ); ?>>30 min</option>
						<option value="600" <?php selected( $gothamadblock_option_cookietime, '600' ); ?>>10 min</option>
						<option value="300" <?php selected( $gothamadblock_option_cookietime, '300' ); ?>>5 min</option>
						<option value="120" <?php selected( $gothamadblock_option_cookietime, '120' ); ?>>2 min</option>
						<option value="60" <?php selected( $gothamadblock_option_cookietime, '60' ); ?>>1 min</option>	
					</select>
			  </tr>
			  
			<tr class="explain">
				<td colspan="2">
			  <h3> <?php echo $txt_adwin_mecha; ?></h3>
			  <p><?php echo $txt_adwin_mecha_p; ?></p>
				</td>
			</tr>
			
			<tr>
				  <td class="libelle"><label for="gothamadblock_option_messageperso_title"><?php echo $txt_adwin_titre; ?> :</label></td>
				  <td><input type="text" id="gothamadblock_option_messageperso_title" name="gothamadblock_option_messageperso_title" value="<?php echo get_option('gothamadblock_option_messageperso_title'); ?>" /></td>
			  </tr>
			  <tr>
				  <td class="libelle"><label for="gothamadblock_option_messageperso"><?php echo $txt_adwin_corpus; ?> :</label></td>
				  <?php $gothamadblock_option_messageperso = get_option('gothamadblock_option_messageperso'); ?>
				<td	><textarea id="gothamadblock_option_messageperso" name="gothamadblock_option_messageperso"><?php echo esc_textarea($gothamadblock_option_messageperso); ?></textarea></td>
			  </tr>
			  <tr>
				  <td class="libelle"><label for="gothamadblock_option_messageperso_button"><?php echo $txt_adwin_cta; ?> :</label></td>
				  <td><input type="text" id="gothamadblock_option_messageperso_button" name="gothamadblock_option_messageperso_button" value="<?php echo get_option('gothamadblock_option_messageperso_button'); ?>" /></td>
			  </tr>
			 
			 
		  </table>

	  <?php submit_button(); ?>
	  </form>
	  </div>
	   <div class="gotham_ad_credit">
						<h3>Gotham Adblock</h3>
						<div class="inside">
							<h4 class="inner"><?php echo $txt_adwin_blokright_title; ?></h4>
							<p class="inner"><?php echo $txt_adwin_blokright_corpus_1; ?></p>
							<ul>
								<li>- <a href="https://wordpress.org/plugins/gotham-block-extra-light/"><?php echo $txt_adwin_blokright_corpus_2; ?></a></li>
								<li>- <a href="https://wordpress.org/support/plugin/gotham-block-extra-light/"><?php echo $txt_adwin_blokright_corpus_3; ?></a></li>
							</ul>
							<hr>
							<h4 class="inner"> <?php echo $txt_adwin_blokright_aime; ?></h4>
							<p class="inner"> <a href="https://wordpress.org/support/plugin/gotham-block-extra-light/reviews/?filter=5#new-post" target="_blank"><?php echo $txt_adwin_blokright_vote; ?></a> <?php echo $txt_adwin_blokright_sur; ?> WordPress.org</p>
							<hr>
							<p class="inner"> Copyright <a href="https://www.kapsulecorp.com/">Kapsule Corp</a></p>
						</div>
		</div>
	</div>
	
	<?php
	
	} // Fin du Init Cave

} // Fin de l'Admin

// Create analytics table on plugin activation
register_activation_hook(__FILE__, 'gotham_create_analytics_table');
function gotham_create_analytics_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_adblock_stats';
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        event_type varchar(50) NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text NOT NULL,
        browser varchar(100) NOT NULL,
        os varchar(50) NOT NULL,
        country varchar(100) NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'pending',
        created_at datetime NOT NULL,
        updated_at datetime NOT NULL,
        PRIMARY KEY  (id),
        KEY ip_address (ip_address),
        KEY status (status)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Add any missing columns if table already exists
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $column_names = array();
    foreach ($columns as $column) {
        $column_names[] = $column->Field;
    }
    
    if (!in_array('status', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN status varchar(20) NOT NULL DEFAULT 'pending' AFTER country");
    }
    if (!in_array('os', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN os varchar(50) NOT NULL AFTER browser");
    }
}

// Load analytics admin module
if (is_admin()) {
    require_once plugin_dir_path(__FILE__) . 'admin/class-gotham-analytics.php';
}

// Enhanced analytics tracking
function gotham_adblock_track_event($event_type = 'pop_displayed', $status = 'pending') {
    global $wpdb;
    
    $ip = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    $browser = gotham_get_browser($user_agent);
    $country = gotham_get_country_by_ip($ip);
    $os = gotham_get_os($user_agent);
    $now = current_time('mysql');
    
    // Check if we already have a pending event for this IP
    $existing = $wpdb->get_row($wpdb->prepare(
        "SELECT id FROM {$wpdb->prefix}gotham_adblock_stats 
         WHERE ip_address = %s AND status = 'pending' ORDER BY id DESC LIMIT 1",
        $ip
    ));
    
    if ($existing && $event_type === 'pop_displayed') {
        // Update existing record if it's a duplicate popup display
        $wpdb->update(
            $wpdb->prefix . 'gotham_adblock_stats',
            array(
                'updated_at' => $now,
                'event_type' => $event_type,
                'user_agent' => $user_agent,
                'browser' => $browser,
                'os' => $os,
                'country' => $country
            ),
            array('id' => $existing->id)
        );
        return $existing->id;
    } else {
        // Insert new record
        $wpdb->insert(
            $wpdb->prefix . 'gotham_adblock_stats',
            array(
                'event_type' => $event_type,
                'ip_address' => $ip,
                'user_agent' => $user_agent,
                'browser' => $browser,
                'os' => $os,
                'country' => $country,
                'status' => $status,
                'created_at' => $now,
                'updated_at' => $now
            )
        );
        return $wpdb->insert_id;
    }
}

// Helper function to get OS from user agent
function gotham_get_os($user_agent) {
    $os_platform = 'Unknown OS';
    $os_array = array(
        '/windows nt 10/i'      =>  'Windows 10',
        '/windows nt 6.3/i'     =>  'Windows 8.1',
        '/windows nt 6.2/i'     =>  'Windows 8',
        '/windows nt 6.1/i'     =>  'Windows 7',
        '/windows nt 6.0/i'     =>  'Windows Vista',
        '/windows nt 5.2/i'     =>  'Windows Server 2003/XP x64',
        '/windows nt 5.1/i'     =>  'Windows XP',
        '/windows xp/i'         =>  'Windows XP',
        '/windows nt 5.0/i'     =>  'Windows 2000',
        '/windows me/i'         =>  'Windows ME',
        '/win98/i'              =>  'Windows 98',
        '/win95/i'              =>  'Windows 95',
        '/win16/i'              =>  'Windows 3.11',
        '/macintosh|mac os x/i' =>  'Mac OS X',
        '/mac_powerpc/i'        =>  'Mac OS 9',
        '/linux/i'              =>  'Linux',
        '/ubuntu/i'             =>  'Ubuntu',
        '/iphone/i'             =>  'iPhone',
        '/ipod/i'               =>  'iPod',
        '/ipad/i'               =>  'iPad',
        '/android/i'            =>  'Android',
        '/blackberry/i'         =>  'BlackBerry',
        '/webos/i'              =>  'Mobile'
    );

    foreach ($os_array as $regex => $value) {
        if (preg_match($regex, $user_agent)) {
            $os_platform = $value;
            break;
        }
    }
    return $os_platform;
}

// Integrate conversion tracking into popup close logic
// This will call the analytics tracker when the user clicks the CTA button to confirm disabling adblock
add_action('wp_footer', function() {
    ?>
    <script>
    // Enhanced conversion tracking with adblock verification
    document.addEventListener('DOMContentLoaded', function() {
        var btn = document.getElementById('gtab_mehn');
        if (btn) {
            btn.addEventListener('click', function() {
                // Set a flag to check for adblock status after a delay
                setTimeout(function() {
                    // Re-run the adblock detection after user claims to have disabled it
                    if (typeof gothamBatAdblock === 'function') {
                        var stillBlocked = gothamBatAdblock();
                        if (!stillBlocked) {
                            // Adblock is actually disabled, track conversion
                            if (typeof window.gotham_adblock_conversion === 'function') {
                                window.gotham_adblock_conversion();
                            }
                        }
                    }
                }, 2000); // Wait 2 seconds for user to disable adblock
            });
        }
    });
    </script>
    <?php
});

// Track analytics events via AJAX
add_action('wp_ajax_nopriv_gotham_adblock_track_event', 'gotham_adblock_track_event');
add_action('wp_ajax_gotham_adblock_track_event', 'gotham_adblock_track_event');
function gotham_adblock_track_event() {
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    $event_type = sanitize_text_field($_POST['event_type'] ?? '');
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // IP-based deduplication: Check if this IP already has this event type today
    $today = date('Y-m-d');
    $existing = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE event_type = %s AND ip_address = %s AND DATE(created_at) = %s",
        $event_type, $ip, $today
    ));

    // For popup_displayed, allow multiple per day but limit to once per hour
    if ($event_type === 'pop_displayed') {
        $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $recent = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE event_type = %s AND ip_address = %s AND created_at > %s",
            $event_type, $ip, $one_hour_ago
        ));
        if ($recent > 0) {
            wp_send_json_success(['message' => 'Event already tracked recently']);
            return;
        }
    }
    // For adblock_disabled, only allow once per day per IP
    elseif ($event_type === 'adblock_disabled' && $existing > 0) {
        wp_send_json_success(['message' => 'Conversion already tracked today for this IP']);
        return;
    }

    $browser = gotham_get_browser($user_agent);
    $country = gotham_get_country_by_ip($ip);
    $os = gotham_get_os($user_agent);
    $now = current_time('mysql');
    
    // Check if we already have a pending event for this IP
    $existing = $wpdb->get_row($wpdb->prepare(
        "SELECT id FROM {$wpdb->prefix}gotham_adblock_stats 
         WHERE ip_address = %s AND status = 'pending' ORDER BY id DESC LIMIT 1",
        $ip
    ));
    
    if ($existing && $event_type === 'pop_displayed') {
        // Update existing record if it's a duplicate popup display
        $wpdb->update(
            $wpdb->prefix . 'gotham_adblock_stats',
            array(
                'updated_at' => $now,
                'event_type' => $event_type,
                'user_agent' => $user_agent,
                'browser' => $browser,
                'os' => $os,
                'country' => $country
            ),
            array('id' => $existing->id)
        );
        return $existing->id;
    } else {
        // Insert new record
        $wpdb->insert(
            $wpdb->prefix . 'gotham_adblock_stats',
            array(
                'event_type' => $event_type,
                'ip_address' => $ip,
                'user_agent' => $user_agent,
                'browser' => $browser,
                'os' => $os,
                'country' => $country,
                'status' => 'pending',
                'created_at' => $now,
                'updated_at' => $now
            )
        );
        return $wpdb->insert_id;
    }
}

// Enhanced browser detection
function gotham_get_browser($user_agent) {
    if (empty($user_agent)) return 'Unknown';

    // More specific detection order matters
    if (strpos($user_agent, 'Edg/') !== false) return 'Edge';
    if (strpos($user_agent, 'OPR/') !== false || strpos($user_agent, 'Opera') !== false) return 'Opera';
    if (strpos($user_agent, 'Firefox/') !== false) return 'Firefox';
    if (strpos($user_agent, 'Chrome/') !== false && strpos($user_agent, 'Safari/') !== false) return 'Chrome';
    if (strpos($user_agent, 'Safari/') !== false && strpos($user_agent, 'Chrome') === false) return 'Safari';
    if (strpos($user_agent, 'MSIE') !== false || strpos($user_agent, 'Trident') !== false) return 'Internet Explorer';
    if (strpos($user_agent, 'Mobile') !== false || strpos($user_agent, 'Android') !== false) return 'Mobile Browser';

    return 'Other';
}

// Enhanced country detection by IP with caching and fallback
function gotham_get_country_by_ip($ip) {
    if (empty($ip) || $ip === '127.0.0.1' || $ip === '::1') {
        return 'Local';
    }

    // Check cache first (transient for 24 hours)
    $cache_key = 'gotham_country_' . md5($ip);
    $cached_country = get_transient($cache_key);
    if ($cached_country !== false) {
        return $cached_country;
    }

    // Try primary API
    $response = wp_remote_get('https://ipapi.co/' . $ip . '/country_name/', [
        'timeout' => 5,
        'user-agent' => 'WordPress/Gotham-Block-Plugin'
    ]);

    if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
        $country = trim(wp_remote_retrieve_body($response));
        if (!empty($country) && $country !== 'Undefined') {
            set_transient($cache_key, $country, 24 * HOUR_IN_SECONDS);
            return $country;
        }
    }

    // Fallback to a different API
    $response = wp_remote_get('http://ip-api.com/line/' . $ip . '?fields=country', [
        'timeout' => 5,
        'user-agent' => 'WordPress/Gotham-Block-Plugin'
    ]);

    if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
        $country = trim(wp_remote_retrieve_body($response));
        if (!empty($country) && $country !== 'fail') {
            set_transient($cache_key, $country, 24 * HOUR_IN_SECONDS);
            return $country;
        }
    }

    // Cache empty result to avoid repeated API calls
    set_transient($cache_key, 'Unknown', HOUR_IN_SECONDS);
    return 'Unknown';
}

// Integrate conversion tracking into popup close logic
// This will call the analytics tracker when the user clicks the CTA button to confirm disabling adblock
add_action('wp_footer', function() {
    ?>
    <script>
    // Enhanced conversion tracking with adblock verification
    document.addEventListener('DOMContentLoaded', function() {
        var btn = document.getElementById('gtab_mehn');
        if (btn) {
            btn.addEventListener('click', function() {
                // Set a flag to check for adblock status after a delay
                setTimeout(function() {
                    // Re-run the adblock detection after user claims to have disabled it
                    if (typeof gothamBatAdblock === 'function') {
                        var stillBlocked = gothamBatAdblock();
                        if (!stillBlocked) {
                            // Adblock is actually disabled, track conversion
                            if (typeof window.gotham_adblock_conversion === 'function') {
                                window.gotham_adblock_conversion();
                            }
                        }
                    }
                }, 2000); // Wait 2 seconds for user to disable adblock
            });
        }
    });
    </script>
    <?php
});



// Remove any custom <script> tags or event dispatches added for analytics tracking
?>