<?php
/**
 * @package Gotham Block Extra Light
 * @version 1.5.0
 */
/*
Plugin Name: Gotham Block Extra Light
Description: 🇬🇧 ULTRA Light plugin to inform your visitors that ad blockers are killing the viability of your site, and invite them to deactivate them 🇫🇷 Plugin ULTRA Léger pour informer tout simplement vos visiteurs que les bloqueurs de publicité tuent la viabilité de votre site, et les invite à les désactiver.
Version: 1.5.0
Author: Kapsule Network
Author URI: https://www.kapsulecorp.com/
License: GPLv2
*/

if ( ! defined( 'ABSPATH' ) )
	exit;

define('GOTHAMBKOCKADBLOCK_ROOTPATH', plugin_dir_path( __FILE__ )); // Chemin Serveur

//////////////////////
// Check PREMIUM //
$needpremium = get_option('gothamadblock_option_premium_tools');
require_once(GOTHAMBKOCKADBLOCK_ROOTPATH.'/premium/valid_api.php');
// Si on a besoin de fonctions PREMIUM => Test de la Validité de la licence //

if ($needpremium == "oui") {
	
	$ghostpremium_doubleface = false;
	$ghostpremium_doubleface = check_kapsuleapi_ghostpremium_licence();

	if ($ghostpremium_doubleface == "cUrl") {
		
		$msgerreur = "Votre serveur n'arrive pas à se connecter à notre API - cURL error 28: Operation timed out - L'erreur semble venir de votre solution d'hébergement car tout fonctionne du côté des serveurs de Gotham Block Adblock";
		$ghostpremium_doubleface = false;
		
	} elseif ($ghostpremium_doubleface == "Erreur") {
		
		$msgerreur = "Votre serveur n'arrive pas à se connecter à notre API"; 
		$ghostpremium_doubleface=false;
		
	 } else {
		 
		 $msgerreur = "";
		 
	}

	define('KINGBOO', $ghostpremium_doubleface); // API ACTIVE ou NON
	
} else {
	
	define('KINGBOO', false);
	
}
// ! Check PREMIUM //
///////////////////////////

// Est-on en pause ?
$gothamadblock_option_fury = get_option('gothamadblock_option_fury');

if ( ($gothamadblock_option_fury != "paused") AND (!is_admin()) ){ // Si on est pas en pause // Ni en mode admin (ce qui créé un confit avec les nouveaux block widgets)
	
	// Est-on en mode FURY ou non ?
	if (($gothamadblock_option_fury == "ssj2") OR ($gothamadblock_option_fury == "ssj3")) { $agressive_mode = true;} else {$agressive_mode = false;}
	//////////////////////////////

	if ((!isset($_COOKIE['gothamadblock_last_visit_time'])) OR ($agressive_mode == true)) { // Si Pas de Cookie ou Si Mode Agressif activé on affiche la Popup

	// Enqueue analytics tracker and bot detection scripts
	function gothamadblock_register_analytics_scripts() {
		wp_enqueue_script('jquery'); // Ensure jQuery is loaded

		// Analytics tracker
		wp_register_script( 'gotham-analytics-tracker', plugins_url( '/analytics-tracker.js', __FILE__ ), array('jquery'), '1.5.0', true );
		wp_enqueue_script( 'gotham-analytics-tracker' );

		// Bot detection tracker
		wp_register_script( 'gotham-bot-detector', plugins_url( '/bot-detection-tracker.js', __FILE__ ), array(), '1.0.0', true );
		wp_enqueue_script( 'gotham-bot-detector' );

		// Get bot detection settings
		$bot_settings = gotham_get_bot_detection_settings();

		// Localize scripts with AJAX URL and settings
		wp_localize_script( 'gotham-analytics-tracker', 'gotham_ajax', array(
			'ajaxurl' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce('gotham_adblock_nonce')
		) );

		wp_localize_script( 'gotham-bot-detector', 'gotham_bot_settings', array(
			'enabled' => $bot_settings['enabled'] === 'oui',
			'minMouseMovements' => intval($bot_settings['min_mouse_movements']),
			'minInteractions' => intval($bot_settings['min_interactions']),
			'gracePeriod' => intval($bot_settings['grace_period']) * 1000, // Convert to milliseconds
			'sessionTimeout' => intval($bot_settings['session_timeout']) * 1000, // Convert to milliseconds
			'sensitivity' => $bot_settings['sensitivity'],
			'trackMouseMovements' => $bot_settings['track_mouse_movements'] === 'oui',
			'trackKeyboardEvents' => $bot_settings['track_keyboard_events'] === 'oui',
			'trackScrollEvents' => $bot_settings['track_scroll_events'] === 'oui'
		) );
	}
	add_action( 'wp_enqueue_scripts', 'gothamadblock_register_analytics_scripts', 0 );
		
		// Popup si Honeypot bloqué par un Adblocker
		function gothamadblock_mapop() {
			$defopiks = plugin_dir_url( __FILE__ ).'stop.png';
			$igotit = plugin_dir_url( __FILE__ ).'ok.png';
			$nonce = wp_create_nonce('gotham_adblock_nonce');
			
			// Track popup display
			gotham_adblock_track_event('pop_displayed', 'pending');

			echo '<script>
			// Make nonce available globally for analytics tracker
			window.gotham_adblock_nonce = "' . $nonce . '";

			function gothamadblock_myClosePop() {
				var mes = document.getElementById("gothamadblock_msg");
				var over = document.getElementById("gothamadblock_overlayh_n");
				mes.style.display = "none";
				over.style.display = "none";
				document.body.classList.remove("gtmab_leviator");

				// Track that user closed without disabling adblock
				jQuery.ajax({
					url: ajaxurl,
					type: "POST",
					data: {
						action: "gotham_adblock_track_event",
						event_type: "popup_closed",
						status: "declined",
						nonce: "' . $nonce . '"
					}
				});
			}

			function gothamadblock_myClosePopSSJ() {
				// Track that user disabled adblock
				jQuery.ajax({
					url: ajaxurl,
					type: "POST",
					data: {
						action: "gotham_adblock_track_event",
						event_type: "adblock_disabled",
						status: "accepted",
						nonce: "' . $nonce . '"
					},
					success: function() {
						window.location.reload();
					}
				});
			}
			</script>';


			//////////// Si pas de message personnalisé

							$gothamadblock_option_messageperso_title = get_option('gothamadblock_option_messageperso_title');
							if ($gothamadblock_option_messageperso_title == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												
												$popup_title = "Adblock détecté";
												
											} else {
												
												$popup_title = "Adblock Detected";
												
											}

										//////////// !Zone de Test Langage
							} else {
								
								$popup_title = $gothamadblock_option_messageperso_title;
							
							}

							$gothamadblock_option_messageperso = get_option('gothamadblock_option_messageperso');
							if ($gothamadblock_option_messageperso == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												$mon_texte_sensibilisation = "<p><u>Ce site internet ne peut exister que grâce à la présence de publicité</u>.<br />Merci de <strong>couper votre logiciel Adblock</Strong> sur ce site et de cliquer sur le bouton j'ai compris.</p>";
											}
											else {
												$mon_texte_sensibilisation = "<p><u>This website can only exist thanks to the presence of advertising</u>.<br />Please <strong>deactivate your Adblock</Strong> software on this site and click on the button I understood.</p>";
											}

										//////////// !Zone de Test Langage
							}
							else {
							$mon_texte_sensibilisation = $gothamadblock_option_messageperso;
							$mon_texte_sensibilisation = wpautop($mon_texte_sensibilisation); // Conversion des sauts de ligne pour corriger le bug du saut de ligne
							}

							$gothamadblock_option_messageperso_button = get_option('gothamadblock_option_messageperso_button');
							if ($gothamadblock_option_messageperso_button == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												$popup_ctatext = "J'ai compris";
											}
											else {

												$popup_ctatext = "I Understood";
											}
										//////////// !Zone de Test Langage
							}
							else {
							$popup_ctatext = $gothamadblock_option_messageperso_button;
							}

			// Est-on en SSJ3 ? Si oui la popup recharge la page et donc revérifie si le adblock est bien coupé, sinon cela coupe la popup.
			$gothamadblock_option_fury = get_option('gothamadblock_option_fury');
			if ($gothamadblock_option_fury == "ssj3") {$janemba="gothamadblock_myClosePopSSJ()";} else {$janemba="gothamadblock_myClosePop()";}
			
			// Construction de la Popup
			$cestbononatout_onconstruit = "<div id='gothamadblock_msg' style='display:block;'><h2>$popup_title</h2><img src='$defopiks' alt='Oing' height='300' width='300' />$mon_texte_sensibilisation<button id='gtab_mehn' onclick='$janemba'>$popup_ctatext</button></div><div id='gothamadblock_overlayh_n' style='display:block;'></div>"; 
			$cestbononatout_onconstruit = str_replace(array("\n", "\r\n", "\r", "\t", "    "), "", $cestbononatout_onconstruit); // On vire tous les sauts de ligne

			
			 // Si bloqué on affiche la popup en JS
			 /* Mécanisme inspiré d'un script de AdGlare Ad Server */
			echo "
			<script>
				function gothamBatAdblock() {
					var a = document.createElement('div');
					a.innerHTML = '&nbsp;';
					a.className = 'gothamads publicite 300x250 text-ad text_ad text_ads text-ads pub_728x90 textAd text-ad-links adsbox moneytizer';
					a.style = 'position: absolute !important; width: 0!important; height: 1px !important; left: -1000px !important; top: -10000px !important;';
					var r = false;
					try {
						document.body.appendChild(a);
						var e = document.getElementsByClassName('gothamads')[0];
						if(e.offsetHeight === 0 || e.clientHeight === 0) r = true;
						if(window.getComputedStyle !== undefined) {
							var tmp = window.getComputedStyle(e, null);
							if(tmp && (tmp.getPropertyValue('display') == 'none' || tmp.getPropertyValue('visibility') == 'hidden')) r = true;
						}
						document.body.removeChild(a);
					} catch (e) {}
					return r;
				}
		   if(gothamBatAdblock()) {
		   document.write(\"$cestbononatout_onconstruit\");
			document.body.classList.add('gtmab_leviator');
		  } 
			</script>";

			  // Si bloqué on affiche le CSS
			echo "<style type='text/css'>
				.gtmab_leviator {height:100%;overflow:hidden;}
				#gothamadblock_msg{position:fixed;width:800px;margin:0 auto;background:#fff;height:auto;display:block;float:left;z-index:99999999;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%);border-radius:8px;border:4px solid orange;padding:40px 0!important;}#gothamadblock_msg img{width:150px;height:150px;margin:20px auto!important;clear:both}#gothamadblock_msg h2{font-weight:700!important;font-family:arial!important;padding:10px 0!important;font-size:26px!important;}#gothamadblock_msg p{margin:30px 0!important;}button#gtab_mehn {cursor:pointer;display: inline-block;text-align: center;vertical-align: middle;padding: 12px 24px;border: 1px solid #4443cf;border-radius: 8px;background: #807eff;background: -webkit-gradient(linear, left top, left bottom, from(#807eff), to(#4443cf));background: -moz-linear-gradient(top, #807eff, #4443cf);background: linear-gradient(to bottom, #807eff, #4443cf);font: normal normal bold 20px arial;color: #ffffff;text-decoration: none;}button#gtab_mehn:focus,button#gtab_mehn:hover{border:1px solid ##504ff4;background:#9a97ff;background:-webkit-gradient(linear,left top,left bottom,from(#9a97ff),to(#5250f8));background:-moz-linear-gradient(top,#9a97ff,#5250f8);background:linear-gradient(to bottom,#9a97ff,#5250f8);color:#fff;text-decoration:none}button#gtab_mehn:active{background:#4443cf;background:-webkit-gradient(linear,left top,left bottom,from(#4443cf),to(#4443cf));background:-moz-linear-gradient(top,#4443cf,#4443cf);background:linear-gradient(to bottom,#4443cf,#4443cf)}button#gtab_mehn:before{content:'';display:inline-block;height:24px;width:24px;line-height:24px;margin:0 4px -6px -4px;position:relative;top:0;left:-3px;background:url($igotit) no-repeat left center transparent;background-size:100% 100%}#gothamadblock_overlayh_n{position:fixed;width:100%;margin:0 auto;opacity:.8;background:#000;height:100%;display:block;float:left;z-index:99999998;top:0;}
				@media only screen and (max-width: 1024px){#gothamadblock_msg{position:fixed;width:90%;margin:0 auto;background:#fff;height:auto;display:block;float:left;z-index:99999999;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%);border-radius:8px;border:4px solid orange;padding:10px;}}@media only screen and (max-width: 767px){#gothamadblock_msg img {width:100px;height:100px;}#gothamadblock_msg {padding:10px!important;}}
				</style>";
			}

			// On lance le plugin
			function gothamadblock_gothamkill() {
				$chosen = gothamadblock_mapop();
				}
			add_action( 'wp_footer', 'gothamadblock_gothamkill' );
	}


	// On dépose un cookie 
	add_action( 'init', 'gothamadblock_add_Cookie' );
	function gothamadblock_add_Cookie() {
		if (!isset($_COOKIE['gothamadblock_last_visit_time'])) { // Si un cookie n'est pas déjà en place et le délai afférant entrain de courir
		$tempsdecuisson = get_option('gothamadblock_option_cookietime'); // On récupère la durée voulue
		if ((empty($tempsdecuisson)) OR ($tempsdecuisson=="")) {$tempsdecuisson=2592000;} // Si paramétrage de la durée de vie du cookie vide, on fixe à 30 jours par défaut
		setcookie("gothamadblock_last_visit_time", "1", time()+$tempsdecuisson, "/"); // On pose le cookie
		}
	}
}

//////////////////////////////////////////////////////////////////////////////////////
// Création du Copyrighting
//////////////////////////////////////////////////////////////////////////////////////
$gothamadblock_option_powered_check = get_option('gothamadblock_option_powered');
if ($gothamadblock_option_powered_check == "oui") {
		function gothamadblock_powered_seo() {
			echo "<p style='text-align:center;'>Plugin <a href='https://www.kapsulecorp.com/' target='_blank' rel='noopener'>Kapsule Corp</a></p>";
			}
		add_action( 'wp_footer', 'gothamadblock_powered_seo' );
}

//////////////////////////////////////////////////////////////////////////////////////
// Création du Menu et de l'enregistrement des options
//////////////////////////////////////////////////////////////////////////////////////

// Si c'est l'admin
if ( is_admin() ){

	///////////////////////////////////
	// On créé les options dans le SQL
	///////////////////////////////////

	function gotham_blockadblock_html_sanitize_callback ($string)
	{
		$gotham_blockadblock_allowed_html = array(
		'a' => array(
			'href' => array(),
			'title' => array(),
			'rel' => array(),
			'target' => array()
		),
		'br' => array(),
		'em' => array(),
		'strong' => array(),
		'b' => array(),
		'u' => array(),
		'strike' => array(),
		'h1' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h2' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h3' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h4' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h5' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'p' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'span' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'ul' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'ol' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'li' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		)
		);	
		
		return wp_kses($string,$gotham_blockadblock_allowed_html);
	}

	add_action( 'admin_init', 'gothamadblock_batarang' );
	function gothamadblock_batarang() {
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_fury', 'gotham_sanitize_fury_mode' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_cookietime', 'gotham_sanitize_cookie_time' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso_title', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso', 'gotham_blockadblock_html_sanitize_callback' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso_button', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_powered', 'gotham_sanitize_yes_no' );
		// PREMIUM API KEY
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_premium_tools', 'gotham_sanitize_yes_no' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_apijeton', 'sanitize_text_field' );

		// BOT DETECTION SETTINGS
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_detection_enabled', 'gotham_sanitize_yes_no' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_min_mouse_movements', 'gotham_sanitize_positive_int' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_min_interactions', 'gotham_sanitize_positive_int' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_grace_period', 'gotham_sanitize_positive_int' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_session_timeout', 'gotham_sanitize_positive_int' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_detection_sensitivity', 'gotham_sanitize_bot_sensitivity' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_exclude_from_analytics', 'gotham_sanitize_yes_no' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_track_mouse_movements', 'gotham_sanitize_yes_no' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_track_keyboard_events', 'gotham_sanitize_yes_no' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_bot_track_scroll_events', 'gotham_sanitize_yes_no' );

		add_action('admin_enqueue_scripts', 'check_licence_update_ghostpremium');
	}

	// Enhanced sanitization functions
	function gotham_sanitize_fury_mode($input) {
	    $valid_modes = array('ssj1', 'ssj2', 'ssj3', 'paused');
	    return in_array($input, $valid_modes) ? $input : 'ssj1';
	}

	function gotham_sanitize_cookie_time($input) {
	    $valid_times = array('60', '120', '300', '600', '1800', '3600', '7200', '86400', '172800', '604800', '1296000', '2592000');
	    return in_array($input, $valid_times) ? $input : '2592000';
	}

	function gotham_sanitize_yes_no($input) {
	    return ($input === 'oui' || $input === 'yes') ? 'oui' : 'non';
	}

	// Bot detection sanitization functions
	function gotham_sanitize_positive_int($input) {
	    $value = intval($input);
	    return $value > 0 ? $value : 1;
	}

	function gotham_sanitize_bot_sensitivity($input) {
	    $valid_sensitivities = array('low', 'medium', 'high');
	    return in_array($input, $valid_sensitivities) ? $input : 'medium';
	}

	// Get bot detection settings with defaults
	function gotham_get_bot_detection_settings() {
	    return array(
	        'enabled' => get_option('gothamadblock_bot_detection_enabled', 'oui'),
	        'min_mouse_movements' => get_option('gothamadblock_bot_min_mouse_movements', 3),
	        'min_interactions' => get_option('gothamadblock_bot_min_interactions', 1),
	        'grace_period' => get_option('gothamadblock_bot_grace_period', 30), // seconds
	        'session_timeout' => get_option('gothamadblock_bot_session_timeout', 300), // seconds
	        'sensitivity' => get_option('gothamadblock_bot_detection_sensitivity', 'medium'),
	        'exclude_from_analytics' => get_option('gothamadblock_bot_exclude_from_analytics', 'oui'),
	        'track_mouse_movements' => get_option('gothamadblock_bot_track_mouse_movements', 'oui'),
	        'track_keyboard_events' => get_option('gothamadblock_bot_track_keyboard_events', 'oui'),
	        'track_scroll_events' => get_option('gothamadblock_bot_track_scroll_events', 'oui')
	    );
	}

	///////////////////////////////////
	// On créé le menu
	//////////////////////////////////

	add_action('admin_menu','gothamadblock_setupmenu');
	function gothamadblock_setupmenu(){
		  add_menu_page(
		      'Configuration de Gotham Block Adblock',
		      'G BlockAdblock',
		      'manage_options', // Changed from 'administrator' to 'manage_options' for better security
		      'gotham-plugin',
		      'gothamadblock_init_cave',
		      'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgZW5hYmxlLWJhY2tncm91bmQ9Im5ldyAwIDAgMjQgMjQiIGlkPSJMYXllcl8xIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+PHBhdGggZD0iTTE2LjUsMTYuNSAgYy03LTMtOSwzLTksM2MtNS41LTItNyw0LTcsNGMwLTkuNSw0LTEzLDQtMTNzLTEsMywyLDNzMS45OTk4Njg0LTQuNSwwLTVoMC4yOTI4OTM5ICBjMC40NTI3NTI2LDAsMC44ODY5NjE1LTAuMTc5ODU1MywxLjIwNzEwNTYtMC40OTk5OTlMOC4wMDAwMDEsNy45OTk5OTk1QzguMzIwMTQ0Nyw3LjY3OTg1NTMsOC41LDcuMjQ1NjQ2NSw4LjUsNi43OTI4OTM5VjYuNSAgYzAuNSwxLjk5OTg2ODQsNSwzLDUsMHMtMy0yLTMtMnMzLjUtNCwxMy00YzAsMC02LDEuNS00LDdDMTkuNSw3LjUsMTMuNSw5LjUsMTYuNSwxNi41IiBmaWxsPSJub25lIiBzdHJva2U9IiMzMDNDNDIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLW1pdGVybGltaXQ9IjEwIi8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PC9zdmc+'
		  );
	}

	///////////////////////////////////
	// On charge le JS de l'admin
	//////////////////////////////////

	function gothamadblock_monjsdansladmin() {
	echo "<style>
	.gotham_ad_wrap #logo_admin {text-align:center;background:black;color:#ac5b5b;padding:80px;border-radius:8px;}
	.gotham_ad_wrap{margin: 10px 20px 0 2px;}
	.gotham_ad_form{float: left;width: 79%;}
	.gotham_ad_credit{float: left;width:17%;background:#fff;box-shadow: 0 0 0 1px rgba(0,0,0,0.05);padding:1%;margin-left:1%;}
	#batbaseadmin tr td.libelle{font-weight:bold;width:250px;}
	#batbaseadmin input, #batbaseadmin select, #batbaseadmin textarea {width:280px;float:left;}
	.explain {background:white;box-shadow: 0 0 0 1px rgba(0,0,0,0.05);}
	.explain p{padding:10px;}
	.explain ul{padding: 0 10px;list-style: square inside;}
	.explain li{padding:10px 0;}
	.explain h3 {padding:6px 10px;border-bottom:1px solid #eee;}
	</style>";

	echo "<script>function besoindelacuisiniere(sel) {var gotham_cuisiniere_ssj = sel.value;if (gotham_cuisiniere_ssj == 'ssj1') {document.getElementById('gothamadblock_option_cookietime').style.display = 'block';} else { document.getElementById('gothamadblock_option_cookietime').style.display = 'none';}} </script><script>
		window.onload = function () {
		document.getElementById('need_premium_or_not').addEventListener('change', function () {
			if (this.value == 'oui') {
				document.getElementById('hidou').style.display = 'table-row';
			} else {
				document.getElementById('hidou').style.display = 'none';
			}
		}, false)
		};
		</script>";

	}

	add_action('admin_enqueue_scripts', 'gothamadblock_monjsdansladmin');

	// Création de la page d'options du plugin ////////////////
	function gothamadblock_init_cave(){
	    // Security check - ensure user has proper capabilities
	    if (!current_user_can('manage_options')) {
	        wp_die(__('You do not have sufficient permissions to access this page.'));
	    }

	///////////////////////////////////////
	/// Mini zone de langage pour l'admin
	///////////////////////////////////////

	///FRANCAIS
	if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
		$txt_adwin_welcome = " Bienvenue dans la BatBase ";
		$txt_adwin_yes = "Oui";
		$txt_adwin_no = "Non";
		$txt_adwin_ssj1 = "SSJ1 Light";
		$txt_adwin_ssj2 = "SSJ2 Agressive";
		$txt_adwin_ssj3 = "SSJ3 Fury";
		$txt_adwin_paused = "Pause ";
		$txt_adwin_titre = "Titre personnalisé";
		$txt_adwin_corpus = "Texte personnalisé (HTML autorisé)";
		$txt_adwin_cta = "Bouton personnalisé";
		$txt_adwin_firemode = "1. Je choisis le degré d'agressivité du plugin !";
		$txt_adwin_firemode_p = "<p>Dans tous les cas, la popup informera l'internaute que bloquer les publicités tue votre buisness model et l'invitera à désactiver son adblocker. Mais vous pouvez choisir ci-dessous le comportement de la popup en fixant son degré d'agressivité selon 3 niveaux.</p><ul><li> SSJ1 (Choix par défaut) : Le message ne s'affichera qu'une fois tous les x minutes/heures/jours (30 jours par défaut), tant que l'internaute aura un logiciel Adblock activé, mais l'internaute pourra fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)</li><li> SSJ2 : Le message s'affichera à chaque chargement de page, tant que l'internaute aura un logiciel Adblock activé, cependant l'internaute pourra à chaque fois fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)</li><li> SSJ3 : Le message s'affichera à chaque chargement de page, et l'internaute ne pourra pas naviguer sur votre site tant qu'il aura un logiciel Adblock activé. Rends la navigation impossible avec Adblock !</li></ul>";
		$txt_adwin_mecha = "2. Je customise (ou pas) la popup !";
		$txt_adwin_mecha_p = "Saisissez le texte de votre choix ou laissez vide pour afficher le texte par défaut.";
	}
	///ANGLAIS
	else {
		$txt_adwin_welcome = "Welcome to the BatBase ";
		$txt_adwin_yes = "Yes";
		$txt_adwin_no = "No";
		$txt_adwin_ssj1 = "SSJ1 Light";
		$txt_adwin_ssj2 = "SSJ2 Agressive";
		$txt_adwin_ssj3 = "SSJ3 Fury";
		$txt_adwin_paused = "Pause ";
		$txt_adwin_titre = "Customized Headline";
		$txt_adwin_corpus = "Customized message (HTML allowed)";
		$txt_adwin_cta = "Customized Button";
		$txt_adwin_firemode = "1. I choose the degree of aggressiveness of the plugin";
		$txt_adwin_firemode_p = "<p>In any case, the popup will inform the user that blocking ads kills your buisness model and will invite him to deactivate his adblocker. But you can choose below the behavior of the popup by setting its degree of aggressiveness according to 3 levels.</p><ul><li> SSJ1 (Default) : The message will only be displayed once every X minute/hours/days (30 days by default), as long as the user has Adblock software activated, but the user can close the popup and continue browsing your site normally (even with Adblock activated)</li><li> SSJ2 : The message will appear on each page load, as long as the user has Adblock software activated, however the user can each time close the popup and continue browsing your site normally (even with Adblock activated)</li><li> SSJ3 : The message will appear on each page load, and the user will not be able to navigate on your site as long as he has Adblock software activated. This level of aggressiveness makes navigation impossible with Adblock!</li></ul>";
		$txt_adwin_mecha = "2. I customize (or not) the popup";
		$txt_adwin_mecha_p = "Enter your text or leave blank for display the default text";
	}
	////////////////////////////////////////

	?>
	<div class="gotham_ad_wrap">
	  <h1 id="logo_admin"><?php echo $txt_adwin_welcome; ?></h1>

	  <div class="gotham_ad_form">
	  <form method="post" action="options.php">
	  <?php settings_fields( 'gothamadblockbat-settings-group' ); ?>
	  <?php do_settings_sections('gothamadblockbat-settings-group'); ?>


		  <table id="batbaseadmin">
				<tr class="explain">
				<td colspan="2">
			  <h3> <?php echo $txt_adwin_firemode; ?></h3>
			  <?php echo $txt_adwin_firemode_p; ?>
				</td>
				</tr>
			  <tr>
				  <td class="libelle"><label for="gothamadblock_option_fury">Fury Mode :</label></td>
				  <td>
					<?php $gothamadblock_option_fury = get_option('gothamadblock_option_fury'); ?>
					<select id="gothamadblock_option_fury" name="gothamadblock_option_fury" value="<?php echo get_option('gothamadblock_option_fury'); ?>" onchange="besoindelacuisiniere(this)">
						<option value="ssj1" <?php selected( $gothamadblock_option_fury, 'ssj1' ); ?>><?php echo $txt_adwin_ssj1; ?></option>
						<option value="ssj2" <?php selected( $gothamadblock_option_fury, 'ssj2' ); ?>><?php echo $txt_adwin_ssj2; ?></option>
						<option value="ssj3" <?php selected( $gothamadblock_option_fury, 'ssj3' ); ?>><?php echo $txt_adwin_ssj3; ?></option>
						<option value="paused" <?php selected( $gothamadblock_option_fury, 'paused' ); ?>><?php echo $txt_adwin_paused; ?></option>
					</select>
					<?php $gothamadblock_option_cookietime = get_option('gothamadblock_option_cookietime'); ?>
					<select id="gothamadblock_option_cookietime" name="gothamadblock_option_cookietime" value="<?php echo get_option('gothamadblock_option_cookietime'); ?>" <?php if ($gothamadblock_option_fury != "ssj1") { ?>style="display:none;"<?php } ?>>
						<option value="2592000" <?php selected( $gothamadblock_option_cookietime, '2592000' ); ?>>30 <?php echo $txt_adwin_mot_jours; ?> (Default)</option>
						<option value="1296000" <?php selected( $gothamadblock_option_cookietime, '1296000' ); ?>>15 <?php echo $txt_adwin_mot_jours; ?></option>
						<option value="604800" <?php selected( $gothamadblock_option_cookietime, '604800' ); ?>>7 <?php echo $txt_adwin_mot_jours; ?></option>
						<option value="172800" <?php selected( $gothamadblock_option_cookietime, '172800' ); ?>>48H</option>
						<option value="86400" <?php selected( $gothamadblock_option_cookietime, '86400' ); ?>>24H</option>
						<option value="7200" <?php selected( $gothamadblock_option_cookietime, '7200' ); ?>>2H</option>
						<option value="3600" <?php selected( $gothamadblock_option_cookietime, '3600' ); ?>>1H</option>
						<option value="1800" <?php selected( $gothamadblock_option_cookietime, '1800' ); ?>>30 min</option>
						<option value="600" <?php selected( $gothamadblock_option_cookietime, '600' ); ?>>10 min</option>
						<option value="300" <?php selected( $gothamadblock_option_cookietime, '300' ); ?>>5 min</option>
						<option value="120" <?php selected( $gothamadblock_option_cookietime, '120' ); ?>>2 min</option>
						<option value="60" <?php selected( $gothamadblock_option_cookietime, '60' ); ?>>1 min</option>	
					</select>
			  </tr>
			  
			<tr class="explain">
				<td colspan="2">
			  <h3> <?php echo $txt_adwin_mecha; ?></h3>
			  <p><?php echo $txt_adwin_mecha_p; ?></p>
				</td>
			</tr>
			
			<tr>
				  <td class="libelle"><label for="gothamadblock_option_messageperso_title"><?php echo $txt_adwin_titre; ?> :</label></td>
				  <td><input type="text" id="gothamadblock_option_messageperso_title" name="gothamadblock_option_messageperso_title" value="<?php echo get_option('gothamadblock_option_messageperso_title'); ?>" /></td>
			  </tr>
			  <tr>
				  <td class="libelle"><label for="gothamadblock_option_messageperso"><?php echo $txt_adwin_corpus; ?> :</label></td>
				  <?php $gothamadblock_option_messageperso = get_option('gothamadblock_option_messageperso'); ?>
				<td	><textarea id="gothamadblock_option_messageperso" name="gothamadblock_option_messageperso"><?php echo esc_textarea($gothamadblock_option_messageperso); ?></textarea></td>
			  </tr>
			  <tr>
				  <td class="libelle"><label for="gothamadblock_option_messageperso_button"><?php echo $txt_adwin_cta; ?> :</label></td>
				  <td><input type="text" id="gothamadblock_option_messageperso_button" name="gothamadblock_option_messageperso_button" value="<?php echo get_option('gothamadblock_option_messageperso_button'); ?>" /></td>
			  </tr>

			  <!-- Bot Detection Settings Section -->
			  <tr class="explain">
				<td colspan="2">
				  <h3>3. Bot Detection Settings</h3>
				  <p>Configure advanced bot detection to improve analytics accuracy by filtering out automated traffic.</p>
				</td>
			  </tr>

			  <tr>
				  <td class="libelle"><label for="gothamadblock_bot_detection_enabled">Enable Bot Detection:</label></td>
				  <td>
					<?php $bot_enabled = get_option('gothamadblock_bot_detection_enabled', 'oui'); ?>
					<select id="gothamadblock_bot_detection_enabled" name="gothamadblock_bot_detection_enabled">
						<option value="oui" <?php selected( $bot_enabled, 'oui' ); ?>><?php echo $txt_adwin_yes; ?></option>
						<option value="non" <?php selected( $bot_enabled, 'non' ); ?>><?php echo $txt_adwin_no; ?></option>
					</select>
				  </td>
			  </tr>

			  <tr>
				  <td class="libelle"><label for="gothamadblock_bot_detection_sensitivity">Detection Sensitivity:</label></td>
				  <td>
					<?php $bot_sensitivity = get_option('gothamadblock_bot_detection_sensitivity', 'medium'); ?>
					<select id="gothamadblock_bot_detection_sensitivity" name="gothamadblock_bot_detection_sensitivity">
						<option value="low" <?php selected( $bot_sensitivity, 'low' ); ?>>Low (Less Strict)</option>
						<option value="medium" <?php selected( $bot_sensitivity, 'medium' ); ?>>Medium (Recommended)</option>
						<option value="high" <?php selected( $bot_sensitivity, 'high' ); ?>>High (More Strict)</option>
					</select>
				  </td>
			  </tr>

			  <tr>
				  <td class="libelle"><label for="gothamadblock_bot_min_mouse_movements">Min Mouse Movements:</label></td>
				  <td><input type="number" id="gothamadblock_bot_min_mouse_movements" name="gothamadblock_bot_min_mouse_movements" value="<?php echo get_option('gothamadblock_bot_min_mouse_movements', 3); ?>" min="0" max="50" /></td>
			  </tr>

			  <tr>
				  <td class="libelle"><label for="gothamadblock_bot_grace_period">Grace Period (seconds):</label></td>
				  <td><input type="number" id="gothamadblock_bot_grace_period" name="gothamadblock_bot_grace_period" value="<?php echo get_option('gothamadblock_bot_grace_period', 30); ?>" min="10" max="300" /></td>
			  </tr>

			  <tr>
				  <td class="libelle"><label for="gothamadblock_bot_exclude_from_analytics">Exclude Bots from Analytics:</label></td>
				  <td>
					<?php $bot_exclude = get_option('gothamadblock_bot_exclude_from_analytics', 'oui'); ?>
					<select id="gothamadblock_bot_exclude_from_analytics" name="gothamadblock_bot_exclude_from_analytics">
						<option value="oui" <?php selected( $bot_exclude, 'oui' ); ?>><?php echo $txt_adwin_yes; ?></option>
						<option value="non" <?php selected( $bot_exclude, 'non' ); ?>><?php echo $txt_adwin_no; ?></option>
					</select>
				  </td>
			  </tr>


		  </table>

	  <?php submit_button(); ?>
	  </form>
	  </div>
	   <div class="gotham_ad_credit">
						<h3>Gotham Adblock</h3>
						<div class="inside">
							<h4 class="inner"><?php echo $txt_adwin_blokright_title; ?></h4>
							<p class="inner"><?php echo $txt_adwin_blokright_corpus_1; ?></p>
							<ul>
								<li>- <a href="https://wordpress.org/plugins/gotham-block-extra-light/"><?php echo $txt_adwin_blokright_corpus_2; ?></a></li>
								<li>- <a href="https://wordpress.org/support/plugin/gotham-block-extra-light/"><?php echo $txt_adwin_blokright_corpus_3; ?></a></li>
							</ul>
							<hr>
							<h4 class="inner"> <?php echo $txt_adwin_blokright_aime; ?></h4>
							<p class="inner"> <a href="https://wordpress.org/support/plugin/gotham-block-extra-light/reviews/?filter=5#new-post" target="_blank"><?php echo $txt_adwin_blokright_vote; ?></a> <?php echo $txt_adwin_blokright_sur; ?> WordPress.org</p>
							<hr>
							<p class="inner"> Copyright <a href="https://www.kapsulecorp.com/">Kapsule Corp</a></p>
						</div>
		</div>
	</div>
	
	<?php
	
	} // Fin du Init Cave

} // Fin de l'Admin

// Create analytics table on plugin activation
register_activation_hook(__FILE__, 'gotham_create_analytics_table');
function gotham_create_analytics_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_adblock_stats';
    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        event_type varchar(50) NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text NOT NULL,
        browser varchar(100) NOT NULL,
        browser_version varchar(50) NOT NULL DEFAULT '',
        os varchar(50) NOT NULL,
        country varchar(100) NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'pending',
        user_type varchar(20) NOT NULL DEFAULT 'unknown',
        session_id varchar(100) NOT NULL DEFAULT '',
        referrer_url text NOT NULL DEFAULT '',
        page_url text NOT NULL DEFAULT '',
        session_duration int(11) NOT NULL DEFAULT 0,
        is_mobile tinyint(1) NOT NULL DEFAULT 0,
        screen_resolution varchar(20) NOT NULL DEFAULT '',
        timezone varchar(50) NOT NULL DEFAULT '',
        bot_classification varchar(20) NOT NULL DEFAULT 'unknown',
        bot_score decimal(5,2) NOT NULL DEFAULT 0.00,
        mouse_movements int(11) NOT NULL DEFAULT 0,
        mouse_clicks int(11) NOT NULL DEFAULT 0,
        scroll_events int(11) NOT NULL DEFAULT 0,
        keyboard_events int(11) NOT NULL DEFAULT 0,
        focus_events int(11) NOT NULL DEFAULT 0,
        page_focus_time int(11) NOT NULL DEFAULT 0,
        interaction_patterns text NOT NULL DEFAULT '',
        behavioral_flags text NOT NULL DEFAULT '',
        classification_timestamp datetime NULL,
        grace_period_active tinyint(1) NOT NULL DEFAULT 1,
        created_at datetime NOT NULL,
        updated_at datetime NOT NULL,
        PRIMARY KEY  (id),
        KEY ip_address (ip_address),
        KEY status (status),
        KEY event_type (event_type),
        KEY created_at (created_at),
        KEY user_type (user_type),
        KEY session_id (session_id),
        KEY bot_classification (bot_classification),
        KEY bot_score (bot_score),
        KEY date_event (DATE(created_at), event_type),
        KEY ip_event_date (ip_address, event_type, created_at),
        KEY user_journey (ip_address, session_id, created_at),
        KEY session_bot (session_id, bot_classification)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Ensure all required columns exist with proper schema
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $column_names = array();
    foreach ($columns as $column) {
        $column_names[] = $column->Field;
    }

    // Add missing columns if they don't exist
    $new_columns = [
        'status' => "ALTER TABLE $table_name ADD COLUMN status varchar(20) NOT NULL DEFAULT 'pending' AFTER country",
        'os' => "ALTER TABLE $table_name ADD COLUMN os varchar(50) NOT NULL AFTER browser",
        'updated_at' => "ALTER TABLE $table_name ADD COLUMN updated_at datetime NOT NULL AFTER created_at",
        'browser_version' => "ALTER TABLE $table_name ADD COLUMN browser_version varchar(50) NOT NULL DEFAULT '' AFTER browser",
        'user_type' => "ALTER TABLE $table_name ADD COLUMN user_type varchar(20) NOT NULL DEFAULT 'unknown' AFTER status",
        'session_id' => "ALTER TABLE $table_name ADD COLUMN session_id varchar(100) NOT NULL DEFAULT '' AFTER user_type",
        'referrer_url' => "ALTER TABLE $table_name ADD COLUMN referrer_url text NOT NULL DEFAULT '' AFTER session_id",
        'page_url' => "ALTER TABLE $table_name ADD COLUMN page_url text NOT NULL DEFAULT '' AFTER referrer_url",
        'session_duration' => "ALTER TABLE $table_name ADD COLUMN session_duration int(11) NOT NULL DEFAULT 0 AFTER page_url",
        'is_mobile' => "ALTER TABLE $table_name ADD COLUMN is_mobile tinyint(1) NOT NULL DEFAULT 0 AFTER session_duration",
        'screen_resolution' => "ALTER TABLE $table_name ADD COLUMN screen_resolution varchar(20) NOT NULL DEFAULT '' AFTER is_mobile",
        'timezone' => "ALTER TABLE $table_name ADD COLUMN timezone varchar(50) NOT NULL DEFAULT '' AFTER screen_resolution",
        'bot_classification' => "ALTER TABLE $table_name ADD COLUMN bot_classification varchar(20) NOT NULL DEFAULT 'unknown' AFTER timezone",
        'bot_score' => "ALTER TABLE $table_name ADD COLUMN bot_score decimal(5,2) NOT NULL DEFAULT 0.00 AFTER bot_classification",
        'mouse_movements' => "ALTER TABLE $table_name ADD COLUMN mouse_movements int(11) NOT NULL DEFAULT 0 AFTER bot_score",
        'mouse_clicks' => "ALTER TABLE $table_name ADD COLUMN mouse_clicks int(11) NOT NULL DEFAULT 0 AFTER mouse_movements",
        'scroll_events' => "ALTER TABLE $table_name ADD COLUMN scroll_events int(11) NOT NULL DEFAULT 0 AFTER mouse_clicks",
        'keyboard_events' => "ALTER TABLE $table_name ADD COLUMN keyboard_events int(11) NOT NULL DEFAULT 0 AFTER scroll_events",
        'focus_events' => "ALTER TABLE $table_name ADD COLUMN focus_events int(11) NOT NULL DEFAULT 0 AFTER keyboard_events",
        'page_focus_time' => "ALTER TABLE $table_name ADD COLUMN page_focus_time int(11) NOT NULL DEFAULT 0 AFTER focus_events",
        'interaction_patterns' => "ALTER TABLE $table_name ADD COLUMN interaction_patterns text NOT NULL DEFAULT '' AFTER page_focus_time",
        'behavioral_flags' => "ALTER TABLE $table_name ADD COLUMN behavioral_flags text NOT NULL DEFAULT '' AFTER interaction_patterns",
        'classification_timestamp' => "ALTER TABLE $table_name ADD COLUMN classification_timestamp datetime NULL AFTER behavioral_flags",
        'grace_period_active' => "ALTER TABLE $table_name ADD COLUMN grace_period_active tinyint(1) NOT NULL DEFAULT 1 AFTER classification_timestamp"
    ];

    foreach ($new_columns as $column_name => $sql) {
        if (!in_array($column_name, $column_names)) {
            $wpdb->query($sql);
        }
    }

    // Add indexes if they don't exist
    $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
    $index_names = array();
    foreach ($indexes as $index) {
        $index_names[] = $index->Key_name;
    }

    $new_indexes = [
        'event_type' => "ALTER TABLE $table_name ADD INDEX event_type (event_type)",
        'created_at' => "ALTER TABLE $table_name ADD INDEX created_at (created_at)",
        'user_type' => "ALTER TABLE $table_name ADD INDEX user_type (user_type)",
        'session_id' => "ALTER TABLE $table_name ADD INDEX session_id (session_id)",
        'bot_classification' => "ALTER TABLE $table_name ADD INDEX bot_classification (bot_classification)",
        'bot_score' => "ALTER TABLE $table_name ADD INDEX bot_score (bot_score)",
        'date_event' => "ALTER TABLE $table_name ADD INDEX date_event (DATE(created_at), event_type)",
        'ip_event_date' => "ALTER TABLE $table_name ADD INDEX ip_event_date (ip_address, event_type, created_at)",
        'user_journey' => "ALTER TABLE $table_name ADD INDEX user_journey (ip_address, session_id, created_at)",
        'session_bot' => "ALTER TABLE $table_name ADD INDEX session_bot (session_id, bot_classification)"
    ];

    foreach ($new_indexes as $index_name => $sql) {
        if (!in_array($index_name, $index_names)) {
            $wpdb->query($sql);
        }
    }
}

// Load analytics admin module
if (is_admin()) {
    require_once plugin_dir_path(__FILE__) . 'admin/class-gotham-analytics.php';
}

// Generate or retrieve session ID for user tracking
function gotham_get_session_id() {
    if (isset($_COOKIE['gotham_session_id'])) {
        return sanitize_text_field($_COOKIE['gotham_session_id']);
    }

    $session_id = 'gs_' . uniqid() . '_' . time();
    setcookie('gotham_session_id', $session_id, time() + (24 * 60 * 60), '/'); // 24 hours
    return $session_id;
}

// Classify user type based on previous interactions
function gotham_classify_user($ip_address) {
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Check if user has been seen before
    $previous_visits = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s",
        $ip_address
    ));

    if ($previous_visits == 0) {
        return 'unique'; // First time visitor
    }

    // Check if user has converted before
    $conversions = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND event_type = 'adblock_disabled'",
        $ip_address
    ));

    if ($conversions > 0) {
        return 'converted'; // Previously converted user
    }

    // Check if user has declined before
    $declined = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND event_type = 'popup_closed'",
        $ip_address
    ));

    if ($declined > 0) {
        return 'returning_declined'; // Returning user who previously declined
    }

    return 'returning'; // Returning user
}

// Get additional tracking data
function gotham_get_tracking_data() {
    return [
        'referrer_url' => $_SERVER['HTTP_REFERER'] ?? '',
        'page_url' => $_SERVER['REQUEST_URI'] ?? '',
        'screen_resolution' => '', // Will be filled by JavaScript
        'timezone' => '', // Will be filled by JavaScript
        'session_duration' => 0 // Will be calculated
    ];
}

// Enhanced analytics tracking with improved user tracking
function gotham_adblock_track_event($event_type = 'pop_displayed', $status = 'pending') {
    global $wpdb;

    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    if (empty($ip)) {
        return false;
    }

    $browser_info = gotham_get_browser_info($user_agent);
    $country = gotham_get_country_by_ip($ip);
    $os = gotham_get_os($user_agent);
    $user_type = gotham_classify_user($ip);
    $session_id = gotham_get_session_id();
    $tracking_data = gotham_get_tracking_data();
    $now = current_time('mysql');
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Improved deduplication logic based on event type
    if ($event_type === 'pop_displayed') {
        // For popup displays, check if we already tracked this in the last hour
        $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $recent = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE ip_address = %s AND event_type = %s AND created_at > %s ORDER BY id DESC LIMIT 1",
            $ip, $event_type, $one_hour_ago
        ));

        if ($recent) {
            // Update the existing recent record instead of creating a new one
            $wpdb->update(
                $table,
                array(
                    'updated_at' => $now,
                    'user_agent' => $user_agent,
                    'browser' => $browser_info['name'],
                    'browser_version' => $browser_info['version'],
                    'os' => $os,
                    'country' => $country,
                    'user_type' => $user_type,
                    'session_id' => $session_id,
                    'referrer_url' => $tracking_data['referrer_url'],
                    'page_url' => $tracking_data['page_url'],
                    'is_mobile' => $browser_info['is_mobile'] ? 1 : 0
                ),
                array('id' => $recent)
            );
            return $recent;
        }
    } elseif ($event_type === 'adblock_disabled') {
        // For conversions, check if we already have one today
        $today = date('Y-m-d');
        $existing_conversion = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE ip_address = %s AND event_type = %s AND DATE(created_at) = %s",
            $ip, $event_type, $today
        ));

        if ($existing_conversion) {
            return $existing_conversion; // Don't create duplicate conversions
        }

        // Update any pending popup records for this IP to 'accepted' status
        $wpdb->update(
            $table,
            array('status' => 'accepted', 'updated_at' => $now),
            array('ip_address' => $ip, 'status' => 'pending')
        );
    } elseif ($event_type === 'popup_closed') {
        // Update any pending popup records for this IP to 'declined' status
        $wpdb->update(
            $table,
            array('status' => 'declined', 'updated_at' => $now),
            array('ip_address' => $ip, 'status' => 'pending')
        );
    }

    // Insert new record with enhanced data
    $result = $wpdb->insert(
        $table,
        array(
            'event_type' => $event_type,
            'ip_address' => $ip,
            'user_agent' => $user_agent,
            'browser' => $browser_info['name'],
            'browser_version' => $browser_info['version'],
            'os' => $os,
            'country' => $country,
            'status' => $status,
            'user_type' => $user_type,
            'session_id' => $session_id,
            'referrer_url' => $tracking_data['referrer_url'],
            'page_url' => $tracking_data['page_url'],
            'session_duration' => $tracking_data['session_duration'],
            'is_mobile' => $browser_info['is_mobile'] ? 1 : 0,
            'screen_resolution' => $tracking_data['screen_resolution'],
            'timezone' => $tracking_data['timezone'],
            'created_at' => $now,
            'updated_at' => $now
        )
    );

    return $result ? $wpdb->insert_id : false;
}

// Helper function to get OS from user agent
function gotham_get_os($user_agent) {
    $os_platform = 'Unknown OS';
    $os_array = array(
        '/windows nt 10/i'      =>  'Windows 10',
        '/windows nt 6.3/i'     =>  'Windows 8.1',
        '/windows nt 6.2/i'     =>  'Windows 8',
        '/windows nt 6.1/i'     =>  'Windows 7',
        '/windows nt 6.0/i'     =>  'Windows Vista',
        '/windows nt 5.2/i'     =>  'Windows Server 2003/XP x64',
        '/windows nt 5.1/i'     =>  'Windows XP',
        '/windows xp/i'         =>  'Windows XP',
        '/windows nt 5.0/i'     =>  'Windows 2000',
        '/windows me/i'         =>  'Windows ME',
        '/win98/i'              =>  'Windows 98',
        '/win95/i'              =>  'Windows 95',
        '/win16/i'              =>  'Windows 3.11',
        '/macintosh|mac os x/i' =>  'Mac OS X',
        '/mac_powerpc/i'        =>  'Mac OS 9',
        '/linux/i'              =>  'Linux',
        '/ubuntu/i'             =>  'Ubuntu',
        '/iphone/i'             =>  'iPhone',
        '/ipod/i'               =>  'iPod',
        '/ipad/i'               =>  'iPad',
        '/android/i'            =>  'Android',
        '/blackberry/i'         =>  'BlackBerry',
        '/webos/i'              =>  'Mobile'
    );

    foreach ($os_array as $regex => $value) {
        if (preg_match($regex, $user_agent)) {
            $os_platform = $value;
            break;
        }
    }
    return $os_platform;
}

// Integrate conversion tracking into popup close logic
// This will call the analytics tracker when the user clicks the CTA button to confirm disabling adblock
add_action('wp_footer', function() {
    ?>
    <script>
    // Enhanced conversion tracking with adblock verification
    document.addEventListener('DOMContentLoaded', function() {
        var btn = document.getElementById('gtab_mehn');
        if (btn) {
            btn.addEventListener('click', function() {
                // Set a flag to check for adblock status after a delay
                setTimeout(function() {
                    // Re-run the adblock detection after user claims to have disabled it
                    if (typeof gothamBatAdblock === 'function') {
                        var stillBlocked = gothamBatAdblock();
                        if (!stillBlocked) {
                            // Adblock is actually disabled, track conversion
                            if (typeof window.gotham_adblock_conversion === 'function') {
                                window.gotham_adblock_conversion();
                            }
                        }
                    }
                }, 2000); // Wait 2 seconds for user to disable adblock
            });
        }
    });
    </script>
    <?php
});

// AJAX handler for tracking adblock events
add_action('wp_ajax_nopriv_gotham_adblock_track_event', 'gotham_ajax_track_event');
add_action('wp_ajax_gotham_adblock_track_event', 'gotham_ajax_track_event');
function gotham_ajax_track_event() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'gotham_adblock_nonce')) {
        wp_send_json_error(['message' => 'Invalid nonce']);
        return;
    }

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    $event_type = sanitize_text_field($_POST['event_type'] ?? '');
    $status = sanitize_text_field($_POST['status'] ?? 'pending');
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Get additional client-side data
    $screen_resolution = sanitize_text_field($_POST['screen_resolution'] ?? '');
    $timezone = sanitize_text_field($_POST['timezone'] ?? '');
    $session_duration = intval($_POST['session_duration'] ?? 0);
    $page_url = esc_url_raw($_POST['page_url'] ?? '');
    $referrer_url = esc_url_raw($_POST['referrer_url'] ?? '');

    if (empty($event_type)) {
        wp_send_json_error(['message' => 'Event type is required']);
        return;
    }

    // IP-based deduplication logic
    $today = date('Y-m-d');

    // For popup_displayed, allow multiple per day but limit to once per hour
    if ($event_type === 'pop_displayed') {
        $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $recent = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE event_type = %s AND ip_address = %s AND created_at > %s",
            $event_type, $ip, $one_hour_ago
        ));
        if ($recent > 0) {
            wp_send_json_success(['message' => 'Event already tracked recently']);
            return;
        }
    }
    // For adblock_disabled, only allow once per day per IP
    elseif ($event_type === 'adblock_disabled') {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE event_type = %s AND ip_address = %s AND DATE(created_at) = %s",
            $event_type, $ip, $today
        ));
        if ($existing > 0) {
            wp_send_json_success(['message' => 'Conversion already tracked today for this IP']);
            return;
        }
    }

    // Get additional data
    $browser_info = gotham_get_browser_info($user_agent);
    $country = gotham_get_country_by_ip($ip);
    $os = gotham_get_os($user_agent);
    $user_type = gotham_classify_user($ip);
    $session_id = gotham_get_session_id();
    $now = current_time('mysql');

    // Override tracking data with client-side data if available
    $tracking_data = [
        'referrer_url' => $referrer_url ?: ($_SERVER['HTTP_REFERER'] ?? ''),
        'page_url' => $page_url ?: ($_SERVER['REQUEST_URI'] ?? ''),
        'screen_resolution' => $screen_resolution,
        'timezone' => $timezone,
        'session_duration' => $session_duration
    ];

    // For popup_displayed events, check if we should update existing pending record
    if ($event_type === 'pop_displayed') {
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM $table WHERE ip_address = %s AND status = 'pending' ORDER BY id DESC LIMIT 1",
            $ip
        ));

        if ($existing) {
            // Update existing record
            $result = $wpdb->update(
                $table,
                array(
                    'updated_at' => $now,
                    'event_type' => $event_type,
                    'user_agent' => $user_agent,
                    'browser' => $browser,
                    'os' => $os,
                    'country' => $country
                ),
                array('id' => $existing->id)
            );
            wp_send_json_success(['message' => 'Event updated', 'id' => $existing->id]);
            return;
        }
    }

    // Insert new record with enhanced data including bot detection fields
    $result = $wpdb->insert(
        $table,
        array(
            'event_type' => $event_type,
            'ip_address' => $ip,
            'user_agent' => $user_agent,
            'browser' => $browser_info['name'],
            'browser_version' => $browser_info['version'],
            'os' => $os,
            'country' => $country,
            'status' => $status,
            'user_type' => $user_type,
            'session_id' => $session_id,
            'referrer_url' => $tracking_data['referrer_url'],
            'page_url' => $tracking_data['page_url'],
            'session_duration' => $tracking_data['session_duration'],
            'is_mobile' => $browser_info['is_mobile'] ? 1 : 0,
            'screen_resolution' => $tracking_data['screen_resolution'],
            'timezone' => $tracking_data['timezone'],
            'bot_classification' => 'unknown', // Will be updated by bot detection
            'bot_score' => 0.00,
            'mouse_movements' => 0,
            'mouse_clicks' => 0,
            'scroll_events' => 0,
            'keyboard_events' => 0,
            'focus_events' => 0,
            'page_focus_time' => 0,
            'interaction_patterns' => '',
            'behavioral_flags' => '',
            'classification_timestamp' => null,
            'grace_period_active' => 1, // Start with grace period active
            'created_at' => $now,
            'updated_at' => $now
        )
    );

    if ($result === false) {
        wp_send_json_error(['message' => 'Failed to insert event']);
    } else {
        wp_send_json_success(['message' => 'Event tracked successfully', 'id' => $wpdb->insert_id]);
    }
}

// AJAX handler for the legacy action name (for backward compatibility)
add_action('wp_ajax_nopriv_gotham_track_adblock_action', 'gotham_ajax_track_legacy_action');
add_action('wp_ajax_gotham_track_adblock_action', 'gotham_ajax_track_legacy_action');
function gotham_ajax_track_legacy_action() {
    // Map legacy action to new handler
    $_POST['event_type'] = $_POST['event_type'] ?? 'popup_closed';
    gotham_ajax_track_event();
}

// AJAX handler for processing behavioral data from bot detection
add_action('wp_ajax_nopriv_gotham_process_behavioral_data', 'gotham_ajax_process_behavioral_data');
add_action('wp_ajax_gotham_process_behavioral_data', 'gotham_ajax_process_behavioral_data');
function gotham_ajax_process_behavioral_data() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'gotham_adblock_nonce')) {
        wp_send_json_error(['message' => 'Invalid nonce']);
        return;
    }

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    if (empty($ip)) {
        wp_send_json_error(['message' => 'IP address required']);
        return;
    }

    // Get behavioral data from POST
    $behavioral_data = [
        'mouse_movements' => intval($_POST['mouseMovements'] ?? 0),
        'mouse_clicks' => intval($_POST['mouseClicks'] ?? 0),
        'scroll_events' => intval($_POST['scrollEvents'] ?? 0),
        'keyboard_events' => intval($_POST['keyboardEvents'] ?? 0),
        'focus_events' => intval($_POST['focusEvents'] ?? 0),
        'page_focus_time' => intval($_POST['pageFocusTime'] ?? 0),
        'session_duration' => intval($_POST['sessionDuration'] ?? 0),
        'interaction_patterns' => sanitize_textarea_field($_POST['interactionPatterns'] ?? ''),
        'behavioral_flags' => sanitize_textarea_field($_POST['behavioralFlags'] ?? ''),
        'event_type' => sanitize_text_field($_POST['eventType'] ?? 'behavioral_update')
    ];

    // Get classification data if provided
    $classification_data = null;
    if (isset($_POST['classification'])) {
        $classification_data = [
            'score' => floatval($_POST['classification']['score'] ?? 0),
            'classification' => sanitize_text_field($_POST['classification']['classification'] ?? 'unknown'),
            'confidence' => floatval($_POST['classification']['confidence'] ?? 0)
        ];
    }

    // Process the behavioral data
    $result = gotham_process_bot_classification($ip, $user_agent, $behavioral_data, $classification_data);

    if ($result) {
        wp_send_json_success(['message' => 'Behavioral data processed successfully', 'classification' => $result]);
    } else {
        wp_send_json_error(['message' => 'Failed to process behavioral data']);
    }
}

// Enhanced browser detection with version information
function gotham_get_browser_info($user_agent) {
    if (empty($user_agent)) return ['name' => 'Unknown', 'version' => '', 'is_mobile' => false];

    $user_agent_lower = strtolower($user_agent);
    $is_mobile = (strpos($user_agent_lower, 'mobile') !== false ||
                  strpos($user_agent_lower, 'android') !== false ||
                  strpos($user_agent_lower, 'iphone') !== false ||
                  strpos($user_agent_lower, 'ipad') !== false);

    // Edge detection
    if (preg_match('/edg\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Microsoft Edge', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }
    if (preg_match('/edge\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Microsoft Edge Legacy', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Opera detection
    if (preg_match('/opr\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Opera', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }
    if (preg_match('/opera\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Opera', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Brave detection
    if (strpos($user_agent_lower, 'brave') !== false) {
        if (preg_match('/chrome\/([0-9.]+)/i', $user_agent, $matches)) {
            return ['name' => 'Brave', 'version' => $matches[1], 'is_mobile' => $is_mobile];
        }
        return ['name' => 'Brave', 'version' => '', 'is_mobile' => $is_mobile];
    }

    // Vivaldi detection
    if (preg_match('/vivaldi\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Vivaldi', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Firefox detection
    if (preg_match('/firefox\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Firefox', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Chrome detection (must be after other Chromium-based browsers)
    if (preg_match('/chrome\/([0-9.]+)/i', $user_agent, $matches) && strpos($user_agent_lower, 'safari') !== false) {
        return ['name' => 'Chrome', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Safari detection
    if (preg_match('/version\/([0-9.]+).*safari/i', $user_agent, $matches) && strpos($user_agent_lower, 'chrome') === false) {
        return ['name' => 'Safari', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Internet Explorer detection
    if (preg_match('/msie ([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Internet Explorer', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }
    if (preg_match('/rv:([0-9.]+).*trident/i', $user_agent, $matches)) {
        return ['name' => 'Internet Explorer', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    return ['name' => 'Other', 'version' => '', 'is_mobile' => $is_mobile];
}

// Backward compatibility function
function gotham_get_browser($user_agent) {
    $browser_info = gotham_get_browser_info($user_agent);
    return $browser_info['name'];
}

// Process bot classification based on behavioral data
function gotham_process_bot_classification($ip, $user_agent, $behavioral_data, $classification_data = null) {
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    $session_id = gotham_get_session_id();
    $now = current_time('mysql');

    // Get bot detection settings
    $bot_settings = gotham_get_bot_detection_settings();

    if ($bot_settings['enabled'] !== 'oui') {
        return false; // Bot detection disabled
    }

    // Server-side bot classification (if not provided by client)
    if (!$classification_data) {
        $classification_data = gotham_server_side_bot_classification($behavioral_data, $bot_settings);
    }

    // Find existing session record
    $existing_record = $wpdb->get_row($wpdb->prepare(
        "SELECT id FROM $table WHERE session_id = %s AND ip_address = %s ORDER BY id DESC LIMIT 1",
        $session_id, $ip
    ));

    if ($existing_record) {
        // Update existing record with behavioral data
        $result = $wpdb->update(
            $table,
            array(
                'mouse_movements' => $behavioral_data['mouse_movements'],
                'mouse_clicks' => $behavioral_data['mouse_clicks'],
                'scroll_events' => $behavioral_data['scroll_events'],
                'keyboard_events' => $behavioral_data['keyboard_events'],
                'focus_events' => $behavioral_data['focus_events'],
                'page_focus_time' => $behavioral_data['page_focus_time'],
                'session_duration' => $behavioral_data['session_duration'],
                'interaction_patterns' => $behavioral_data['interaction_patterns'],
                'behavioral_flags' => $behavioral_data['behavioral_flags'],
                'bot_classification' => $classification_data['classification'],
                'bot_score' => $classification_data['score'],
                'classification_timestamp' => $now,
                'grace_period_active' => 0,
                'updated_at' => $now
            ),
            array('id' => $existing_record->id)
        );
    } else {
        // Create new record with behavioral data
        $browser_info = gotham_get_browser_info($user_agent);
        $country = gotham_get_country_by_ip($ip);
        $os = gotham_get_os($user_agent);
        $user_type = gotham_classify_user($ip);

        $result = $wpdb->insert(
            $table,
            array(
                'event_type' => $behavioral_data['event_type'],
                'ip_address' => $ip,
                'user_agent' => $user_agent,
                'browser' => $browser_info['name'],
                'browser_version' => $browser_info['version'],
                'os' => $os,
                'country' => $country,
                'status' => 'behavioral_analysis',
                'user_type' => $user_type,
                'session_id' => $session_id,
                'session_duration' => $behavioral_data['session_duration'],
                'is_mobile' => $browser_info['is_mobile'] ? 1 : 0,
                'mouse_movements' => $behavioral_data['mouse_movements'],
                'mouse_clicks' => $behavioral_data['mouse_clicks'],
                'scroll_events' => $behavioral_data['scroll_events'],
                'keyboard_events' => $behavioral_data['keyboard_events'],
                'focus_events' => $behavioral_data['focus_events'],
                'page_focus_time' => $behavioral_data['page_focus_time'],
                'interaction_patterns' => $behavioral_data['interaction_patterns'],
                'behavioral_flags' => $behavioral_data['behavioral_flags'],
                'bot_classification' => $classification_data['classification'],
                'bot_score' => $classification_data['score'],
                'classification_timestamp' => $now,
                'grace_period_active' => 0,
                'created_at' => $now,
                'updated_at' => $now
            )
        );
    }

    return $result ? $classification_data : false;
}

// Server-side bot classification algorithm
function gotham_server_side_bot_classification($behavioral_data, $settings) {
    $score = 0;
    $flags = json_decode($behavioral_data['behavioral_flags'], true) ?: [];

    // Check minimum interactions
    $total_interactions = $behavioral_data['mouse_movements'] +
                         $behavioral_data['mouse_clicks'] +
                         $behavioral_data['keyboard_events'] +
                         $behavioral_data['scroll_events'];

    if ($total_interactions < intval($settings['min_interactions'])) {
        $score += 30;
    }

    if ($behavioral_data['mouse_movements'] < intval($settings['min_mouse_movements'])) {
        $score += 25;
    }

    // Check for zero interactions (strong bot indicator)
    if ($total_interactions === 0) {
        $score += 50;
    }

    // Behavioral flags scoring
    $flag_scores = [
        'linear_mouse_movement' => 20,
        'identical_timing' => 25,
        'too_fast_movement' => 15,
        'no_focus_events' => 10,
        'suspicious_patterns' => 15
    ];

    foreach ($flags as $flag) {
        if (isset($flag_scores[$flag])) {
            $score += $flag_scores[$flag];
        }
    }

    // Focus time analysis
    if ($behavioral_data['session_duration'] > 0) {
        $focus_ratio = $behavioral_data['page_focus_time'] / $behavioral_data['session_duration'];
        if ($focus_ratio < 0.1) { // Less than 10% focus time
            $score += 20;
        }
    }

    // No focus events at all
    if ($behavioral_data['focus_events'] === 0) {
        $score += 15;
    }

    // Adjust based on sensitivity
    $thresholds = [
        'low' => 70,
        'medium' => 50,
        'high' => 30
    ];

    $threshold = $thresholds[$settings['sensitivity']] ?? 50;
    $classification = $score >= $threshold ? 'bot' : 'human';

    return [
        'score' => min($score, 100),
        'classification' => $classification,
        'confidence' => min($score / $threshold, 1.0)
    ];
}

// Enhanced country detection by IP with caching and multiple fallbacks
function gotham_get_country_by_ip($ip) {
    if (empty($ip) || $ip === '127.0.0.1' || $ip === '::1' || filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        return 'Local';
    }

    // Check cache first (transient for 24 hours)
    $cache_key = 'gotham_country_' . md5($ip);
    $cached_country = get_transient($cache_key);
    if ($cached_country !== false) {
        return $cached_country;
    }

    $country = 'Unknown';
    $apis = [
        'https://ipapi.co/' . $ip . '/country_name/',
        'http://ip-api.com/line/' . $ip . '?fields=country',
        'https://ipinfo.io/' . $ip . '/country'
    ];

    foreach ($apis as $api_url) {
        $response = wp_remote_get($api_url, [
            'timeout' => 3,
            'user-agent' => 'WordPress/Gotham-Block-Plugin',
            'sslverify' => false // For compatibility with some servers
        ]);

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $result = trim(wp_remote_retrieve_body($response));

            // Validate the result
            if (!empty($result) &&
                $result !== 'Undefined' &&
                $result !== 'fail' &&
                $result !== 'error' &&
                strlen($result) > 1 &&
                strlen($result) < 100) {

                $country = $result;
                break;
            }
        }

        // Small delay between API calls to be respectful
        usleep(100000); // 0.1 seconds
    }

    // Cache the result (even if Unknown) to avoid repeated API calls
    $cache_duration = ($country === 'Unknown') ? HOUR_IN_SECONDS : 24 * HOUR_IN_SECONDS;
    set_transient($cache_key, $country, $cache_duration);

    return $country;
}

// Integrate conversion tracking into popup close logic
// This will call the analytics tracker when the user clicks the CTA button to confirm disabling adblock
add_action('wp_footer', function() {
    ?>
    <script>
    // Enhanced conversion tracking with adblock verification
    document.addEventListener('DOMContentLoaded', function() {
        var btn = document.getElementById('gtab_mehn');
        if (btn) {
            btn.addEventListener('click', function() {
                // Set a flag to check for adblock status after a delay
                setTimeout(function() {
                    // Re-run the adblock detection after user claims to have disabled it
                    if (typeof gothamBatAdblock === 'function') {
                        var stillBlocked = gothamBatAdblock();
                        if (!stillBlocked) {
                            // Adblock is actually disabled, track conversion
                            if (typeof window.gotham_adblock_conversion === 'function') {
                                window.gotham_adblock_conversion();
                            }
                        }
                    }
                }, 2000); // Wait 2 seconds for user to disable adblock
            });
        }
    });
    </script>
    <?php
});



// Remove any custom <script> tags or event dispatches added for analytics tracking
?>