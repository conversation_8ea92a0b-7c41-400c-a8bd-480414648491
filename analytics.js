// analytics.js - <PERSON>les dashboard data fetching and chart rendering

document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('gotham-analytics-dashboard')) return;
    fetch(ajaxurl + '?action=gotham_adblock_get_stats')
        .then(res => res.json())
        .then(data => renderAnalyticsChart(data));
});

// Add advanced analytics: country and browser breakdown
function renderAnalyticsChart(data) {
    const ctx = document.getElementById('gotham-analytics-chart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Pop Displayed', 'Adblock Disabled'],
            datasets: [{
                label: 'Events',
                data: [data.pop_displayed, data.adblock_disabled],
                backgroundColor: ['#ff9800', '#4caf50']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { display: false },
                title: { display: true, text: 'Adblocker Popup & Conversion Stats' }
            }
        }
    });
    // Country breakdown
    if (data.countries) {
        let countries = Object.keys(data.countries);
        let counts = Object.values(data.countries);
        let countryDiv = document.createElement('div');
        countryDiv.innerHTML = '<h3>By Country</h3>';
        let list = '<ul>' + countries.map((c,i) => `<li>${c}: ${counts[i]}</li>`).join('') + '</ul>';
        countryDiv.innerHTML += list;
        document.getElementById('gotham-analytics-dashboard').appendChild(countryDiv);
    }
    // Browser breakdown
    if (data.browsers) {
        let browsers = Object.keys(data.browsers);
        let counts = Object.values(data.browsers);
        let browserDiv = document.createElement('div');
        browserDiv.innerHTML = '<h3>By Browser</h3>';
        let list = '<ul>' + browsers.map((b,i) => `<li>${b}: ${counts[i]}</li>`).join('') + '</ul>';
        browserDiv.innerHTML += list;
        document.getElementById('gotham-analytics-dashboard').appendChild(browserDiv);
    }
}
