// bot-detection-tracker.js - Advanced behavioral tracking for bot detection

class GothamBotDetector {
    constructor() {
        this.behaviorData = {
            mouseMovements: 0,
            mouseClicks: 0,
            scrollEvents: 0,
            keyboardEvents: 0,
            focusEvents: 0,
            pageFocusTime: 0,
            interactionPatterns: [],
            behavioralFlags: [],
            sessionStart: Date.now(),
            lastActivity: Date.now(),
            isPageVisible: true
        };
        
        this.settings = {
            enabled: true,
            minMouseMovements: 3,
            minInteractions: 1,
            gracePeriod: 30000, // 30 seconds
            sessionTimeout: 300000, // 5 minutes
            sensitivity: 'medium',
            trackMouseMovements: true,
            trackKeyboardEvents: true,
            trackScrollEvents: true
        };
        
        this.timers = {
            gracePeriod: null,
            sessionTimeout: null,
            focusTimer: null
        };
        
        this.isClassified = false;
        this.init();
    }
    
    init() {
        try {
            // Load settings from server if available
            this.loadSettings();

            if (!this.settings.enabled) {
                console.log('Gotham Bot Detection: Disabled via settings');
                return;
            }

            // Initialize tracking with error handling
            this.setupEventListeners();
            this.startGracePeriod();
            this.startSessionTimeout();
            this.startFocusTracking();

            console.log('Gotham Bot Detection: Initialized successfully');
        } catch (error) {
            console.warn('Gotham Bot Detection: Failed to initialize, continuing without bot detection', error);
            // Graceful degradation - continue without bot detection
        }
    }
    
    loadSettings() {
        // Settings will be localized from PHP
        if (typeof gotham_bot_settings !== 'undefined') {
            this.settings = { ...this.settings, ...gotham_bot_settings };
        }
    }
    
    setupEventListeners() {
        try {
            // Mouse movement tracking
            if (this.settings.trackMouseMovements) {
                document.addEventListener('mousemove', this.handleMouseMove.bind(this), { passive: true });
                document.addEventListener('click', this.handleMouseClick.bind(this), { passive: true });
            }

            // Keyboard tracking
            if (this.settings.trackKeyboardEvents) {
                document.addEventListener('keydown', this.handleKeyboard.bind(this), { passive: true });
                document.addEventListener('keyup', this.handleKeyboard.bind(this), { passive: true });
            }

            // Scroll tracking
            if (this.settings.trackScrollEvents) {
                document.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
                window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
            }

            // Focus and visibility tracking
            document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
            window.addEventListener('focus', this.handleFocus.bind(this));
            window.addEventListener('blur', this.handleBlur.bind(this));

            // Touch events for mobile
            document.addEventListener('touchstart', this.handleTouch.bind(this), { passive: true });
            document.addEventListener('touchmove', this.handleTouch.bind(this), { passive: true });

            // Page unload tracking
            window.addEventListener('beforeunload', this.handlePageUnload.bind(this));
        } catch (error) {
            console.warn('Gotham Bot Detection: Error setting up event listeners', error);
            // Continue without some tracking features
        }
    }
    
    handleMouseMove(event) {
        this.behaviorData.mouseMovements++;
        this.behaviorData.lastActivity = Date.now();
        
        // Record movement pattern
        this.recordInteractionPattern('mouse_move', {
            x: event.clientX,
            y: event.clientY,
            timestamp: Date.now()
        });
        
        // Check for suspicious patterns
        this.checkMousePatterns(event);
    }
    
    handleMouseClick(event) {
        this.behaviorData.mouseClicks++;
        this.behaviorData.lastActivity = Date.now();
        
        this.recordInteractionPattern('mouse_click', {
            x: event.clientX,
            y: event.clientY,
            button: event.button,
            timestamp: Date.now()
        });
    }
    
    handleKeyboard(event) {
        this.behaviorData.keyboardEvents++;
        this.behaviorData.lastActivity = Date.now();
        
        this.recordInteractionPattern('keyboard', {
            type: event.type,
            key: event.key,
            timestamp: Date.now()
        });
    }
    
    handleScroll(event) {
        this.behaviorData.scrollEvents++;
        this.behaviorData.lastActivity = Date.now();
        
        this.recordInteractionPattern('scroll', {
            scrollY: window.scrollY,
            scrollX: window.scrollX,
            timestamp: Date.now()
        });
    }
    
    handleTouch(event) {
        // Treat touch events as mouse interactions for mobile
        this.behaviorData.mouseMovements++;
        this.behaviorData.lastActivity = Date.now();
        
        this.recordInteractionPattern('touch', {
            type: event.type,
            touches: event.touches.length,
            timestamp: Date.now()
        });
    }
    
    handleFocus() {
        this.behaviorData.focusEvents++;
        this.behaviorData.isPageVisible = true;
        this.startFocusTracking();
    }
    
    handleBlur() {
        this.behaviorData.isPageVisible = false;
        this.stopFocusTracking();
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            this.behaviorData.isPageVisible = false;
            this.stopFocusTracking();
        } else {
            this.behaviorData.isPageVisible = true;
            this.behaviorData.focusEvents++;
            this.startFocusTracking();
        }
    }
    
    handlePageUnload() {
        this.stopFocusTracking();
        this.sendBehaviorData('page_unload');
    }
    
    startFocusTracking() {
        if (this.timers.focusTimer) {
            clearInterval(this.timers.focusTimer);
        }
        
        this.timers.focusTimer = setInterval(() => {
            if (this.behaviorData.isPageVisible) {
                this.behaviorData.pageFocusTime += 1000; // Add 1 second
            }
        }, 1000);
    }
    
    stopFocusTracking() {
        if (this.timers.focusTimer) {
            clearInterval(this.timers.focusTimer);
            this.timers.focusTimer = null;
        }
    }
    
    recordInteractionPattern(type, data) {
        this.behaviorData.interactionPatterns.push({
            type: type,
            data: data,
            timestamp: Date.now()
        });
        
        // Keep only last 50 patterns to avoid memory issues
        if (this.behaviorData.interactionPatterns.length > 50) {
            this.behaviorData.interactionPatterns.shift();
        }
    }
    
    checkMousePatterns(event) {
        // Check for suspicious mouse patterns
        const patterns = this.behaviorData.interactionPatterns.filter(p => p.type === 'mouse_move');
        
        if (patterns.length >= 10) {
            const recent = patterns.slice(-10);
            
            // Check for perfectly straight lines (bot behavior)
            if (this.isLinearMovement(recent)) {
                this.addBehavioralFlag('linear_mouse_movement');
            }
            
            // Check for identical timing patterns
            if (this.hasIdenticalTiming(recent)) {
                this.addBehavioralFlag('identical_timing');
            }
            
            // Check for too-fast movement
            if (this.isTooFastMovement(recent)) {
                this.addBehavioralFlag('too_fast_movement');
            }
        }
    }
    
    isLinearMovement(patterns) {
        if (patterns.length < 3) return false;
        
        let linearCount = 0;
        for (let i = 2; i < patterns.length; i++) {
            const p1 = patterns[i-2].data;
            const p2 = patterns[i-1].data;
            const p3 = patterns[i].data;
            
            // Check if points are roughly in a straight line
            const slope1 = (p2.y - p1.y) / (p2.x - p1.x);
            const slope2 = (p3.y - p2.y) / (p3.x - p2.x);
            
            if (Math.abs(slope1 - slope2) < 0.1) {
                linearCount++;
            }
        }
        
        return linearCount > patterns.length * 0.7; // 70% linear movements
    }
    
    hasIdenticalTiming(patterns) {
        if (patterns.length < 5) return false;
        
        const intervals = [];
        for (let i = 1; i < patterns.length; i++) {
            intervals.push(patterns[i].timestamp - patterns[i-1].timestamp);
        }
        
        // Check if most intervals are identical
        const uniqueIntervals = [...new Set(intervals)];
        return uniqueIntervals.length <= 2; // Very few unique intervals
    }
    
    isTooFastMovement(patterns) {
        if (patterns.length < 2) return false;
        
        let fastMovements = 0;
        for (let i = 1; i < patterns.length; i++) {
            const p1 = patterns[i-1].data;
            const p2 = patterns[i].data;
            const distance = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
            const time = p2.timestamp - p1.timestamp;
            const speed = distance / time; // pixels per millisecond
            
            if (speed > 5) { // Very fast movement
                fastMovements++;
            }
        }
        
        return fastMovements > patterns.length * 0.5; // 50% fast movements
    }
    
    addBehavioralFlag(flag) {
        if (!this.behaviorData.behavioralFlags.includes(flag)) {
            this.behaviorData.behavioralFlags.push(flag);
        }
    }
    
    startGracePeriod() {
        this.timers.gracePeriod = setTimeout(() => {
            this.classifyUser();
        }, this.settings.gracePeriod);
    }
    
    startSessionTimeout() {
        this.timers.sessionTimeout = setTimeout(() => {
            this.classifyUser('session_timeout');
        }, this.settings.sessionTimeout);
    }
    
    classifyUser(reason = 'grace_period_ended') {
        if (this.isClassified) return;
        
        this.isClassified = true;
        this.stopFocusTracking();
        
        // Clear timers
        if (this.timers.gracePeriod) clearTimeout(this.timers.gracePeriod);
        if (this.timers.sessionTimeout) clearTimeout(this.timers.sessionTimeout);
        
        const classification = this.calculateBotScore();
        this.sendBehaviorData('classification', { reason, classification });
    }
    
    calculateBotScore() {
        let score = 0;
        let classification = 'human';
        
        // Check minimum interactions
        const totalInteractions = this.behaviorData.mouseMovements + 
                                this.behaviorData.mouseClicks + 
                                this.behaviorData.keyboardEvents + 
                                this.behaviorData.scrollEvents;
        
        if (totalInteractions < this.settings.minInteractions) {
            score += 30;
        }
        
        if (this.behaviorData.mouseMovements < this.settings.minMouseMovements) {
            score += 25;
        }
        
        // Check behavioral flags
        score += this.behaviorData.behavioralFlags.length * 15;
        
        // Check focus time
        const sessionDuration = Date.now() - this.behaviorData.sessionStart;
        const focusRatio = this.behaviorData.pageFocusTime / sessionDuration;
        
        if (focusRatio < 0.1) { // Less than 10% focus time
            score += 20;
        }
        
        // Check for zero interactions
        if (totalInteractions === 0) {
            score += 50;
        }
        
        // Adjust based on sensitivity
        const thresholds = {
            'low': 70,
            'medium': 50,
            'high': 30
        };
        
        const threshold = thresholds[this.settings.sensitivity] || 50;
        
        if (score >= threshold) {
            classification = 'bot';
        }
        
        return {
            score: Math.min(score, 100),
            classification: classification,
            confidence: Math.min(score / threshold, 1.0)
        };
    }
    
    sendBehaviorData(eventType, additionalData = {}) {
        try {
            const data = {
                ...this.behaviorData,
                eventType: eventType,
                sessionDuration: Date.now() - this.behaviorData.sessionStart,
                interactionPatterns: JSON.stringify(this.behaviorData.interactionPatterns),
                behavioralFlags: JSON.stringify(this.behaviorData.behavioralFlags),
                ...additionalData
            };

            // Send to server via existing analytics system
            if (typeof gotham_send_behavioral_data === 'function') {
                gotham_send_behavioral_data(data);
            } else {
                console.warn('Gotham Bot Detection: Behavioral data sender not available');
            }
        } catch (error) {
            console.warn('Gotham Bot Detection: Error sending behavioral data', error);
            // Fail silently to not interfere with main functionality
        }
    }
    
    // Public method to get current behavior data
    getBehaviorData() {
        return {
            ...this.behaviorData,
            sessionDuration: Date.now() - this.behaviorData.sessionStart
        };
    }
    
    // Public method to force classification
    forceClassification() {
        this.classifyUser('manual_trigger');
    }
}

// Initialize bot detector when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Delay initialization slightly to ensure other scripts load first
    setTimeout(function() {
        try {
            window.gothamBotDetector = new GothamBotDetector();
        } catch (error) {
            console.warn('Gotham Bot Detection: Failed to initialize, continuing without bot detection', error);
            // Create a dummy object to prevent errors in other scripts
            window.gothamBotDetector = {
                getBehaviorData: function() { return {}; },
                forceClassification: function() { return false; }
            };
        }
    }, 100); // 100ms delay
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GothamBotDetector;
}
