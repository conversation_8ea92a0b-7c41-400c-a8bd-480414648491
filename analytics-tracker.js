// analytics-tracker.js - Tracks adblock popup and conversion events

function gotham_send_analytics(eventType) {
    if (typeof ajaxurl === 'undefined') {
        console.warn('Gotham Analytics: ajaxurl is not defined!');
        return;
    }
    var data = new FormData();
    data.append('action', 'gotham_adblock_track_event');
    data.append('event_type', eventType);
    fetch(ajaxurl, { method: 'POST', body: data })
        .then(res => res.json())
        .then(json => {
            if (json && json.success) {
                console.log('Gotham Analytics: Event sent:', eventType);
            } else {
                console.warn('Gotham Analytics: Event failed:', eventType, json);
            }
        })
        .catch(e => console.error('Gotham Analytics: AJAX error', e));
}

console.log('Gotham Analytics: tracker script loaded');

// Use MutationObserver to detect popup display and send analytics event
(function() {
    let sent = false;
    function sendIfPopupVisible() {
        if (!sent && document.getElementById('gothamadblock_msg')) {
            sent = true;
            if (typeof gotham_send_analytics === 'function') {
                gotham_send_analytics('pop_displayed');
            }
        }
    }
    const observer = new MutationObserver(sendIfPopupVisible);
    observer.observe(document.body, { childList: true, subtree: true });
    // Also check immediately in case popup is already present
    sendIfPopupVisible();
})();

// Call this when user disables adblock and continues
window.gotham_adblock_conversion = function() {
    gotham_send_analytics('adblock_disabled');
}
