// analytics-tracker.js - Tracks adblock popup and conversion events

// Enhanced analytics tracking with additional client-side data
function gotham_send_analytics(eventType, status = 'pending') {
    // Check for localized AJAX variables first, then fallback to globals
    var ajaxUrl = '';
    var nonce = '';

    if (typeof gotham_ajax !== 'undefined') {
        ajaxUrl = gotham_ajax.ajaxurl;
        nonce = gotham_ajax.nonce;
    } else if (typeof ajaxurl !== 'undefined') {
        ajaxUrl = ajaxurl;
        if (typeof gotham_adblock_nonce !== 'undefined') {
            nonce = gotham_adblock_nonce;
        }
    } else {
        console.warn('Gotham Analytics: AJAX URL not available');
        return;
    }

    // Collect additional client-side data
    var additionalData = gotham_collect_client_data();

    var data = new FormData();
    data.append('action', 'gotham_adblock_track_event');
    data.append('event_type', eventType);
    data.append('status', status);
    data.append('nonce', nonce);

    // Add client-side data
    data.append('screen_resolution', additionalData.screen_resolution);
    data.append('timezone', additionalData.timezone);
    data.append('session_duration', additionalData.session_duration);
    data.append('page_url', additionalData.page_url);
    data.append('referrer_url', additionalData.referrer_url);

    fetch(ajaxUrl, { method: 'POST', body: data })
        .then(res => res.json())
        .then(json => {
            if (json && json.success) {
                console.log('Gotham Analytics: Event sent:', eventType);
            } else {
                console.warn('Gotham Analytics: Event failed:', eventType, json);
            }
        })
        .catch(e => console.error('Gotham Analytics: AJAX error', e));
}

// Collect additional client-side data
function gotham_collect_client_data() {
    var sessionStart = gotham_get_session_start();
    var sessionDuration = sessionStart ? Math.floor((Date.now() - sessionStart) / 1000) : 0;

    return {
        screen_resolution: screen.width + 'x' + screen.height,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || '',
        session_duration: sessionDuration,
        page_url: window.location.href,
        referrer_url: document.referrer || ''
    };
}

// Session management
function gotham_get_session_start() {
    var sessionStart = sessionStorage.getItem('gotham_session_start');
    if (!sessionStart) {
        sessionStart = Date.now();
        sessionStorage.setItem('gotham_session_start', sessionStart);
    }
    return parseInt(sessionStart);
}

// Track page visibility changes for better session tracking
function gotham_track_page_visibility() {
    var hidden = 'hidden';
    var visibilityChange = 'visibilitychange';

    if (typeof document.hidden !== 'undefined') {
        hidden = 'hidden';
        visibilityChange = 'visibilitychange';
    } else if (typeof document.msHidden !== 'undefined') {
        hidden = 'msHidden';
        visibilityChange = 'msvisibilitychange';
    } else if (typeof document.webkitHidden !== 'undefined') {
        hidden = 'webkitHidden';
        visibilityChange = 'webkitvisibilitychange';
    }

    document.addEventListener(visibilityChange, function() {
        if (document[hidden]) {
            // Page is hidden - user might be leaving
            gotham_send_analytics('page_hidden', 'tracking');
        } else {
            // Page is visible again
            gotham_send_analytics('page_visible', 'tracking');
        }
    });
}

console.log('Gotham Analytics: tracker script loaded');

// Initialize tracking when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize session tracking
    gotham_get_session_start();

    // Initialize page visibility tracking
    gotham_track_page_visibility();
});

// Use MutationObserver to detect popup display and send analytics event
(function() {
    let sent = false;
    function sendIfPopupVisible() {
        if (!sent && document.getElementById('gothamadblock_msg')) {
            sent = true;
            if (typeof gotham_send_analytics === 'function') {
                gotham_send_analytics('pop_displayed');
            }
        }
    }
    const observer = new MutationObserver(sendIfPopupVisible);
    observer.observe(document.body, { childList: true, subtree: true });
    // Also check immediately in case popup is already present
    sendIfPopupVisible();
})();

// Call this when user disables adblock and continues
window.gotham_adblock_conversion = function() {
    gotham_send_analytics('adblock_disabled');
}

// Function to send behavioral data for bot detection
window.gotham_send_behavioral_data = function(behaviorData) {
    // Check for localized AJAX variables
    var ajaxUrl = '';
    var nonce = '';

    if (typeof gotham_ajax !== 'undefined') {
        ajaxUrl = gotham_ajax.ajaxurl;
        nonce = gotham_ajax.nonce;
    } else if (typeof ajaxurl !== 'undefined') {
        ajaxUrl = ajaxurl;
        if (typeof gotham_adblock_nonce !== 'undefined') {
            nonce = gotham_adblock_nonce;
        }
    } else {
        console.warn('Gotham Bot Detection: AJAX URL not available');
        return;
    }

    var data = new FormData();
    data.append('action', 'gotham_process_behavioral_data');
    data.append('nonce', nonce);

    // Add behavioral data
    Object.keys(behaviorData).forEach(key => {
        if (typeof behaviorData[key] === 'object') {
            data.append(key, JSON.stringify(behaviorData[key]));
        } else {
            data.append(key, behaviorData[key]);
        }
    });

    fetch(ajaxUrl, { method: 'POST', body: data })
        .then(res => res.json())
        .then(json => {
            if (json && json.success) {
                console.log('Gotham Bot Detection: Behavioral data sent successfully');
            } else {
                console.warn('Gotham Bot Detection: Failed to send behavioral data', json);
            }
        })
        .catch(e => console.error('Gotham Bot Detection: AJAX error', e));
}
