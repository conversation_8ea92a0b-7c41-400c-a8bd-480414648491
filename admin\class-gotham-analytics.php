<?php
/**
 * Gotham Block Analytics Admin Class
 * Handles analytics dashboard, DB, and AJAX for the plugin
 */
if (!defined('ABSPATH')) exit;

class Gotham_Analytics_Admin {
    public function __construct() {
        add_action('admin_menu', [$this, 'add_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('wp_ajax_gotham_adblock_get_stats', [$this, 'ajax_get_stats']);
        // Create table on plugin activation
        $this->create_table();
    }

    public function add_menu() {
        add_menu_page(
            'Adblock Analytics',
            'Adblock Analytics',
            'manage_options',
            'gotham-adblock-analytics',
            [$this, 'dashboard_page'],
            'dashicons-chart-bar',
            56
        );
    }

    public function enqueue_assets($hook) {
        if ($hook !== 'toplevel_page_gotham-adblock-analytics') return;
        wp_enqueue_style('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.css');
        wp_enqueue_script('chartjs', 'https://cdn.jsdelivr.net/npm/chart.js', [], null, true);
        wp_enqueue_script('apexcharts', 'https://cdn.jsdelivr.net/npm/apexcharts', [], null, true);
        wp_enqueue_script('jsvectormap', 'https://cdn.jsdelivr.net/npm/jsvectormap', [], null, true);
        wp_enqueue_script('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.js', ['chartjs','apexcharts','jsvectormap'], null, true);
        wp_localize_script('gotham-analytics-admin', 'gothamAnalyticsAjax', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gotham_analytics')
        ]);
    }

    public function create_table() {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $charset_collate = $wpdb->get_charset_collate();
        $sql = "CREATE TABLE IF NOT EXISTS $table (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            event_type VARCHAR(32),
            ip VARCHAR(64),
            country VARCHAR(64),
            browser VARCHAR(128),
            user_agent TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ) $charset_collate;";
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    public function dashboard_page() {
        echo '<div class="wrap gotham-analytics-admin"><h1>Adblock Analytics</h1>';
        echo '<div id="gotham-analytics-dashboard" class="gotham-analytics-empty"><div class="gotham-analytics-empty-message" style="display:none;"></div></div>';
        echo '</div>';
    }

    public function ajax_get_stats() {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $pop_displayed = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE event_type = %s", 'pop_displayed'));
        $adblock_disabled = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE event_type = %s", 'adblock_disabled'));
        $countries = $wpdb->get_results("SELECT country, COUNT(*) as c FROM $table GROUP BY country ORDER BY c DESC LIMIT 20", ARRAY_A);
        $browsers = $wpdb->get_results("SELECT browser, COUNT(*) as c FROM $table GROUP BY browser ORDER BY c DESC LIMIT 10", ARRAY_A);
        $country_data = [];
        foreach ($countries as $row) { $country_data[$row['country'] ?: 'Unknown'] = (int)$row['c']; }
        $browser_data = [];
        foreach ($browsers as $row) { $browser_data[$row['browser'] ?: 'Unknown'] = (int)$row['c']; }
        wp_send_json([
            'pop_displayed' => intval($pop_displayed),
            'adblock_disabled' => intval($adblock_disabled),
            'countries' => $country_data,
            'browsers' => $browser_data
        ]);
    }
}

// Initialize in admin
if (is_admin()) {
    new Gotham_Analytics_Admin();
}
