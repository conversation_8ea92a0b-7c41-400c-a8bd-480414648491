<?php
/**
 * Gotham Block Analytics Admin Class
 * Handles analytics dashboard, DB, and AJAX for the plugin
 */
if (!defined('ABSPATH')) exit;

class Gotham_Analytics_Admin {
    public function __construct() {
        add_action('admin_menu', [$this, 'add_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('wp_ajax_gotham_adblock_get_stats', [$this, 'ajax_get_stats']);
        // Create table on plugin activation
        $this->create_table();
    }

    public function add_menu() {
        add_menu_page(
            'Adblock Analytics',
            'Adblock Analytics',
            'manage_options',
            'gotham-adblock-analytics',
            [$this, 'dashboard_page'],
            'dashicons-chart-bar',
            56
        );
    }

    public function enqueue_assets($hook) {
        if ($hook !== 'toplevel_page_gotham-adblock-analytics') return;
        wp_enqueue_style('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.css');
        wp_enqueue_script('chartjs', 'https://cdn.jsdelivr.net/npm/chart.js', [], null, true);
        wp_enqueue_script('apexcharts', 'https://cdn.jsdelivr.net/npm/apexcharts', [], null, true);
        wp_enqueue_script('jsvectormap', 'https://cdn.jsdelivr.net/npm/jsvectormap', [], null, true);
        wp_enqueue_script('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.js', ['chartjs','apexcharts','jsvectormap'], null, true);
        wp_localize_script('gotham-analytics-admin', 'gothamAnalyticsAjax', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gotham_analytics')
        ]);
    }

    public function create_table() {
        // Table creation is now handled by the main plugin file
        // This method is kept for backward compatibility but does nothing
        return;
    }

    public function dashboard_page() {
        echo '<div class="wrap gotham-analytics-admin"><h1>Adblock Analytics</h1>';
        echo '<div id="gotham-analytics-dashboard" class="gotham-analytics-empty"><div class="gotham-analytics-empty-message" style="display:none;"></div></div>';
        echo '</div>';
    }

    public function ajax_get_stats() {
        // Verify nonce for security
        if (!wp_verify_nonce($_GET['_wpnonce'] ?? '', 'gotham_analytics')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';

        // Get basic stats with better queries
        $pop_displayed = $wpdb->get_var($wpdb->prepare("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = %s", 'pop_displayed'));
        $adblock_disabled = $wpdb->get_var($wpdb->prepare("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = %s", 'adblock_disabled'));
        $popup_closed = $wpdb->get_var($wpdb->prepare("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = %s", 'popup_closed'));

        // Get conversion rate
        $conversion_rate = $pop_displayed > 0 ? round(($adblock_disabled / $pop_displayed) * 100, 2) : 0;

        // Get countries (from all events to get better data)
        $countries = $wpdb->get_results("SELECT country, COUNT(DISTINCT ip_address) as c FROM $table WHERE country != '' AND country != 'Unknown' GROUP BY country ORDER BY c DESC LIMIT 20", ARRAY_A);
        $country_data = [];
        foreach ($countries as $row) {
            $country_name = $row['country'] ?: 'Unknown';
            $country_data[$country_name] = (int)$row['c'];
        }

        // Get browsers (from all events to get better data)
        $browsers = $wpdb->get_results("SELECT browser, COUNT(DISTINCT ip_address) as c FROM $table WHERE browser != '' AND browser != 'Unknown' GROUP BY browser ORDER BY c DESC LIMIT 10", ARRAY_A);
        $browser_data = [];
        foreach ($browsers as $row) {
            $browser_name = $row['browser'] ?: 'Unknown';
            $browser_data[$browser_name] = (int)$row['c'];
        }

        // Get recent activity (last 30 days)
        $thirty_days_ago = date('Y-m-d H:i:s', strtotime('-30 days'));
        $recent_popups = $wpdb->get_var($wpdb->prepare("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = %s AND created_at > %s", 'pop_displayed', $thirty_days_ago));
        $recent_conversions = $wpdb->get_var($wpdb->prepare("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = %s AND created_at > %s", 'adblock_disabled', $thirty_days_ago));

        wp_send_json([
            'pop_displayed' => intval($pop_displayed),
            'adblock_disabled' => intval($adblock_disabled),
            'popup_closed' => intval($popup_closed),
            'conversion_rate' => $conversion_rate,
            'recent_popups' => intval($recent_popups),
            'recent_conversions' => intval($recent_conversions),
            'countries' => $country_data,
            'browsers' => $browser_data
        ]);
    }
}

// Initialize in admin
if (is_admin()) {
    new Gotham_Analytics_Admin();
}
