<?php
/**
 * Gotham Block Analytics Admin Class
 * Handles analytics dashboard, DB, and AJAX for the plugin
 */
if (!defined('ABSPATH')) exit;

class Gotham_Analytics_Admin {
    public function __construct() {
        add_action('admin_menu', [$this, 'add_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('wp_ajax_gotham_adblock_get_stats', [$this, 'ajax_get_stats']);
        // Create table on plugin activation
        $this->create_table();
    }

    public function add_menu() {
        add_menu_page(
            'Adblock Analytics',
            'Adblock Analytics',
            'manage_options',
            'gotham-adblock-analytics',
            [$this, 'dashboard_page'],
            'dashicons-chart-bar',
            56
        );
    }

    public function enqueue_assets($hook) {
        if ($hook !== 'toplevel_page_gotham-adblock-analytics') return;
        wp_enqueue_style('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.css');
        wp_enqueue_script('chartjs', 'https://cdn.jsdelivr.net/npm/chart.js', [], null, true);
        wp_enqueue_script('apexcharts', 'https://cdn.jsdelivr.net/npm/apexcharts', [], null, true);
        wp_enqueue_script('jsvectormap', 'https://cdn.jsdelivr.net/npm/jsvectormap', [], null, true);
        wp_enqueue_script('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.js', ['chartjs','apexcharts','jsvectormap'], null, true);
        wp_localize_script('gotham-analytics-admin', 'gothamAnalyticsAjax', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gotham_analytics')
        ]);
    }

    public function create_table() {
        // Table creation is now handled by the main plugin file
        // This method is kept for backward compatibility but does nothing
        return;
    }

    public function dashboard_page() {
        ?>
        <div class="wrap gotham-analytics-admin">
            <h1>Adblock Analytics Dashboard</h1>

            <!-- Dashboard Controls -->
            <div class="gotham-controls">
                <div class="control-group">
                    <label for="period-selector">Time Period:</label>
                    <select id="period-selector">
                        <option value="daily">Daily (Last 30 days)</option>
                        <option value="hourly">Hourly (Last 24 hours)</option>
                        <option value="weekly">Weekly (Last 12 weeks)</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="days-selector">Days to Show:</label>
                    <select id="days-selector">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                </div>

                <div class="control-group">
                    <button id="refresh-data" class="button button-primary">Refresh Data</button>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loading-indicator" style="display: none;">
                <p>Loading analytics data...</p>
            </div>

            <!-- Summary Cards -->
            <div id="summary-cards" class="summary-grid">
                <!-- Cards will be populated by JavaScript -->
            </div>

            <!-- Main Dashboard -->
            <div id="gotham-analytics-dashboard" class="dashboard-grid">
                <!-- Charts will be populated by JavaScript -->
            </div>

            <!-- Error Message -->
            <div id="error-message" class="notice notice-error" style="display: none;">
                <p>Failed to load analytics data. Please try again.</p>
            </div>
        </div>
        <?php
    }

    public function ajax_get_stats() {
        // Verify nonce for security
        if (!wp_verify_nonce($_GET['_wpnonce'] ?? '', 'gotham_analytics')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        $period = sanitize_text_field($_GET['period'] ?? 'daily');
        $days = intval($_GET['days'] ?? 30);

        $data = [
            'summary' => $this->get_summary_stats($days),
            'time_series' => $this->get_time_series_data($period, $days),
            'funnel' => $this->get_conversion_funnel($days),
            'geographic' => $this->get_geographic_data($days),
            'browsers' => $this->get_browser_data($days),
            'user_types' => $this->get_user_type_data($days)
        ];

        wp_send_json($data);
    }

    private function get_summary_stats($days = 30) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $stats = [];

        // Get bot detection settings to determine if we should exclude bots
        $bot_settings = gotham_get_bot_detection_settings();
        $bot_detection_enabled = $bot_settings['enabled'] === 'oui';

        if ($bot_detection_enabled) {
            // Only count VERIFIED HUMAN users (bot detection enabled)
            // Total unique HUMAN users who saw popup (only classified humans)
            $stats['total_users'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table
                 WHERE event_type = 'pop_displayed' AND bot_classification = 'human' AND created_at > %s",
                $date_filter
            ));

            // HUMAN users who converted (only classified humans)
            $stats['converted_users'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table
                 WHERE event_type = 'adblock_disabled' AND bot_classification = 'human' AND created_at > %s",
                $date_filter
            ));

            // HUMAN users who declined (only classified humans)
            $stats['declined_users'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table
                 WHERE event_type = 'popup_closed' AND bot_classification = 'human' AND created_at > %s",
                $date_filter
            ));
        } else {
            // Bot detection disabled - count all users (legacy behavior)
            $stats['total_users'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = 'pop_displayed' AND created_at > %s",
                $date_filter
            ));

            $stats['converted_users'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = 'adblock_disabled' AND created_at > %s",
                $date_filter
            ));

            $stats['declined_users'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = 'popup_closed' AND created_at > %s",
                $date_filter
            ));
        }

        // Conversion rate (excluding bots if configured)
        $stats['conversion_rate'] = $stats['total_users'] > 0 ?
            round(($stats['converted_users'] / $stats['total_users']) * 100, 2) : 0;

        // Bot detection statistics
        $stats['bot_stats'] = $this->get_bot_detection_stats($days);

        // User type breakdown
        $user_types = $wpdb->get_results($wpdb->prepare(
            "SELECT user_type, COUNT(DISTINCT ip_address) as count FROM $table
             WHERE created_at > %s GROUP BY user_type",
            $date_filter
        ), ARRAY_A);

        $stats['user_types'] = [];
        foreach ($user_types as $type) {
            $stats['user_types'][$type['user_type']] = intval($type['count']);
        }

        return $stats;
    }

    private function get_bot_detection_stats($days = 30) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $bot_stats = [];

        // Total classified visitors
        $bot_stats['total_classified'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table
             WHERE bot_classification IN ('bot', 'human') AND created_at > %s",
            $date_filter
        ));

        // Bot visitors
        $bot_stats['bot_visitors'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table
             WHERE bot_classification = 'bot' AND created_at > %s",
            $date_filter
        ));

        // Human visitors
        $bot_stats['human_visitors'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table
             WHERE bot_classification = 'human' AND created_at > %s",
            $date_filter
        ));

        // Bot percentage
        $bot_stats['bot_percentage'] = $bot_stats['total_classified'] > 0 ?
            round(($bot_stats['bot_visitors'] / $bot_stats['total_classified']) * 100, 2) : 0;

        // Average bot score
        $bot_stats['avg_bot_score'] = $wpdb->get_var($wpdb->prepare(
            "SELECT AVG(bot_score) FROM $table
             WHERE bot_score > 0 AND created_at > %s",
            $date_filter
        ));
        $bot_stats['avg_bot_score'] = $bot_stats['avg_bot_score'] ? round($bot_stats['avg_bot_score'], 2) : 0;

        // Bot classification breakdown
        $classifications = $wpdb->get_results($wpdb->prepare(
            "SELECT bot_classification, COUNT(DISTINCT ip_address) as count FROM $table
             WHERE created_at > %s GROUP BY bot_classification",
            $date_filter
        ), ARRAY_A);

        $bot_stats['classifications'] = [];
        foreach ($classifications as $classification) {
            $bot_stats['classifications'][$classification['bot_classification']] = intval($classification['count']);
        }

        return $bot_stats;
    }

    private function get_time_series_data($period, $days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';

        switch ($period) {
            case 'hourly':
                return $this->get_hourly_data($days);
            case 'weekly':
                return $this->get_weekly_data($days);
            default:
                return $this->get_daily_data($days);
        }
    }

    private function get_daily_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d', strtotime("-$days days"));

        // Get bot detection settings
        $bot_settings = gotham_get_bot_detection_settings();
        $bot_detection_enabled = $bot_settings['enabled'] === 'oui';

        if ($bot_detection_enabled) {
            // Only count verified humans when bot detection is enabled
            $sql = "SELECT
                        DATE(created_at) as date,
                        event_type,
                        COUNT(DISTINCT ip_address) as count
                    FROM $table
                    WHERE DATE(created_at) >= %s AND bot_classification = 'human'
                    GROUP BY DATE(created_at), event_type
                    ORDER BY date ASC";
        } else {
            // Count all users when bot detection is disabled (legacy behavior)
            $sql = "SELECT
                        DATE(created_at) as date,
                        event_type,
                        COUNT(DISTINCT ip_address) as count
                    FROM $table
                    WHERE DATE(created_at) >= %s
                    GROUP BY DATE(created_at), event_type
                    ORDER BY date ASC";
        }

        $results = $wpdb->get_results($wpdb->prepare($sql, $date_filter), ARRAY_A);

        return $this->format_time_series_data($results, 'date');
    }

    private function get_hourly_data($hours = 24) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$hours hours"));

        // Get bot detection settings
        $bot_settings = gotham_get_bot_detection_settings();
        $bot_detection_enabled = $bot_settings['enabled'] === 'oui';

        if ($bot_detection_enabled) {
            // Only count verified humans when bot detection is enabled
            $sql = "SELECT
                        DATE_FORMAT(created_at, '%%Y-%%m-%%d %%H:00:00') as hour,
                        event_type,
                        COUNT(DISTINCT ip_address) as count
                    FROM $table
                    WHERE created_at >= %s AND bot_classification = 'human'
                    GROUP BY DATE_FORMAT(created_at, '%%Y-%%m-%%d %%H:00:00'), event_type
                    ORDER BY hour ASC";
        } else {
            // Count all users when bot detection is disabled (legacy behavior)
            $sql = "SELECT
                        DATE_FORMAT(created_at, '%%Y-%%m-%%d %%H:00:00') as hour,
                        event_type,
                        COUNT(DISTINCT ip_address) as count
                    FROM $table
                    WHERE created_at >= %s
                    GROUP BY DATE_FORMAT(created_at, '%%Y-%%m-%%d %%H:00:00'), event_type
                    ORDER BY hour ASC";
        }

        $results = $wpdb->get_results($wpdb->prepare($sql, $date_filter), ARRAY_A);

        return $this->format_time_series_data($results, 'hour');
    }

    private function get_weekly_data($weeks = 12) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d', strtotime("-$weeks weeks"));

        // Get bot detection settings
        $bot_settings = gotham_get_bot_detection_settings();
        $bot_detection_enabled = $bot_settings['enabled'] === 'oui';

        if ($bot_detection_enabled) {
            // Only count verified humans when bot detection is enabled
            $sql = "SELECT
                        DATE_FORMAT(created_at, '%%Y-%%u') as week,
                        event_type,
                        COUNT(DISTINCT ip_address) as count
                    FROM $table
                    WHERE DATE(created_at) >= %s AND bot_classification = 'human'
                    GROUP BY DATE_FORMAT(created_at, '%%Y-%%u'), event_type
                    ORDER BY week ASC";
        } else {
            // Count all users when bot detection is disabled (legacy behavior)
            $sql = "SELECT
                        DATE_FORMAT(created_at, '%%Y-%%u') as week,
                        event_type,
                        COUNT(DISTINCT ip_address) as count
                    FROM $table
                    WHERE DATE(created_at) >= %s
                    GROUP BY DATE_FORMAT(created_at, '%%Y-%%u'), event_type
                    ORDER BY week ASC";
        }

        $results = $wpdb->get_results($wpdb->prepare($sql, $date_filter), ARRAY_A);

        return $this->format_time_series_data($results, 'week');
    }

    private function format_time_series_data($results, $time_key) {
        $formatted = [];
        $events = ['pop_displayed', 'adblock_disabled', 'popup_closed'];

        foreach ($results as $row) {
            $time = $row[$time_key];
            if (!isset($formatted[$time])) {
                $formatted[$time] = [
                    'time' => $time,
                    'pop_displayed' => 0,
                    'adblock_disabled' => 0,
                    'popup_closed' => 0
                ];
            }
            $formatted[$time][$row['event_type']] = intval($row['count']);
        }

        return array_values($formatted);
    }

    private function get_conversion_funnel($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        // Get funnel data
        $funnel = [];

        // Get bot detection settings
        $bot_settings = gotham_get_bot_detection_settings();
        $bot_detection_enabled = $bot_settings['enabled'] === 'oui';

        if ($bot_detection_enabled) {
            // Only count verified humans when bot detection is enabled
            // Step 1: HUMAN users who saw popup
            $funnel['popup_displayed'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table
                 WHERE event_type = 'pop_displayed' AND bot_classification = 'human' AND created_at > %s",
                $date_filter
            ));

            // Step 2: HUMAN users who interacted (either converted or declined)
            $funnel['interacted'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table
                 WHERE event_type IN ('adblock_disabled', 'popup_closed') AND bot_classification = 'human' AND created_at > %s",
                $date_filter
            ));

            // Step 3: HUMAN users who converted
            $funnel['converted'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table
                 WHERE event_type = 'adblock_disabled' AND bot_classification = 'human' AND created_at > %s",
                $date_filter
            ));
        } else {
            // Count all users when bot detection is disabled (legacy behavior)
            // Step 1: Users who saw popup
            $funnel['popup_displayed'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = 'pop_displayed' AND created_at > %s",
                $date_filter
            ));

            // Step 2: Users who interacted (either converted or declined)
            $funnel['interacted'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table
                 WHERE event_type IN ('adblock_disabled', 'popup_closed') AND created_at > %s",
                $date_filter
            ));

            // Step 3: Users who converted
            $funnel['converted'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = 'adblock_disabled' AND created_at > %s",
                $date_filter
            ));
        }

        // Calculate percentages
        $funnel['interaction_rate'] = $funnel['popup_displayed'] > 0 ?
            round(($funnel['interacted'] / $funnel['popup_displayed']) * 100, 2) : 0;
        $funnel['conversion_rate'] = $funnel['popup_displayed'] > 0 ?
            round(($funnel['converted'] / $funnel['popup_displayed']) * 100, 2) : 0;

        return $funnel;
    }

    private function get_geographic_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $countries = $wpdb->get_results($wpdb->prepare(
            "SELECT country,
                    COUNT(DISTINCT ip_address) as total_users,
                    SUM(CASE WHEN event_type = 'adblock_disabled' THEN 1 ELSE 0 END) as conversions
             FROM $table
             WHERE country != '' AND country != 'Unknown' AND created_at > %s
             GROUP BY country
             ORDER BY total_users DESC
             LIMIT 20",
            $date_filter
        ), ARRAY_A);

        $geographic_data = [];
        foreach ($countries as $country) {
            $geographic_data[$country['country']] = [
                'users' => intval($country['total_users']),
                'conversions' => intval($country['conversions']),
                'conversion_rate' => $country['total_users'] > 0 ?
                    round(($country['conversions'] / $country['total_users']) * 100, 2) : 0
            ];
        }

        return $geographic_data;
    }

    private function get_browser_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $browsers = $wpdb->get_results($wpdb->prepare(
            "SELECT browser, browser_version,
                    COUNT(DISTINCT ip_address) as total_users,
                    SUM(CASE WHEN event_type = 'adblock_disabled' THEN 1 ELSE 0 END) as conversions,
                    SUM(CASE WHEN is_mobile = 1 THEN 1 ELSE 0 END) as mobile_users
             FROM $table
             WHERE browser != '' AND browser != 'Unknown' AND created_at > %s
             GROUP BY browser, browser_version
             ORDER BY total_users DESC
             LIMIT 15",
            $date_filter
        ), ARRAY_A);

        $browser_data = [];
        foreach ($browsers as $browser) {
            $key = $browser['browser'] . ($browser['browser_version'] ? ' ' . $browser['browser_version'] : '');
            $browser_data[$key] = [
                'users' => intval($browser['total_users']),
                'conversions' => intval($browser['conversions']),
                'mobile_users' => intval($browser['mobile_users']),
                'conversion_rate' => $browser['total_users'] > 0 ?
                    round(($browser['conversions'] / $browser['total_users']) * 100, 2) : 0
            ];
        }

        return $browser_data;
    }

    private function get_user_type_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $user_types = $wpdb->get_results($wpdb->prepare(
            "SELECT user_type,
                    COUNT(DISTINCT ip_address) as total_users,
                    SUM(CASE WHEN event_type = 'adblock_disabled' THEN 1 ELSE 0 END) as conversions
             FROM $table
             WHERE created_at > %s
             GROUP BY user_type
             ORDER BY total_users DESC",
            $date_filter
        ), ARRAY_A);

        $type_data = [];
        foreach ($user_types as $type) {
            $type_data[$type['user_type']] = [
                'users' => intval($type['total_users']),
                'conversions' => intval($type['conversions']),
                'conversion_rate' => $type['total_users'] > 0 ?
                    round(($type['conversions'] / $type['total_users']) * 100, 2) : 0
            ];
        }

        return $type_data;
    }
}

// Initialize in admin
if (is_admin()) {
    new Gotham_Analytics_Admin();
}
